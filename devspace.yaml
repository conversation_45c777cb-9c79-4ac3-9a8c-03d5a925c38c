version: v2beta1
name: invoice-admin

# This is a list of `pipelines` that DevSpace can execute (you can define your own)
pipelines:
  # This is the pipeline for the main command: `devspace dev` (or `devspace run-pipeline dev`)
  dev:
    run: |-
      chmod 755 devspace_start.sh  # Make the script executable
      start_dev app                # Start dev mode "app" (see "dev" section)

# This is a list of `dev` containers that are based on the containers created by your deployments
dev:
  app:
    # Search for the container that runs this image
    labelSelector:
      app.kubernetes.io/name: invoice-admin
    # Replace the container image with this dev-optimized image (allows to skip image building during development)
    devImage: hub.okii.com/base/hotswapagent/jdk21-hotswapagent-jbrsdk:1.0
    # Env can be used to add environment variables to the container
    env:
      - name: SPRING_PROFILES_ACTIVE
        value: test
      - name: HOTSWAP_AGENT
        value: -XX:+AllowEnhancedClassRedefinition -XX:HotswapAgent=fatjar --add-opens=java.base/java.net=ALL-UNNAMED
      - name: REMOTE_DEBUG
        value: -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005
    # Forward the following ports to be able access your application via localhost
    ports:
      - port: 10000:8080
      - port: 20000:5005
    # Sync files between the local filesystem and the development container
    sync:
      - path: devspace.yaml
        file: true
      - path: devspace_start.sh
        file: true
      - path: "start/target/start.jar:start.jar"
        file: true
      - path: invoice-admin-adapter/target/classes:classes/adapter
      - path: invoice-admin-app/target/classes:classes/app
      - path: invoice-admin-client/target/classes:classes/client
      - path: invoice-admin-domain/target/classes:classes/domain
      - path: invoice-admin-infrastructure/target/classes:classes/infrastructure
      - path: invoice-admin-common/target/classes:classes/common
      - path: start/target/classes:classes/start
    # Open a terminal and use the following command to start it
    terminal:
      command: ./devspace_start.sh
    # Make the following commands from my local machine available inside the dev container
    proxyCommands:
      - command: devspace
      - command: kubectl
      - gitCredentials: true

# Use the `commands` section to define repeatable dev workflows for this project
commands:
  start:
    description: Start the application.
    command: |
      devspace enter --pod $HOSTNAME -c invoice-admin -- bash -c '
        java $JAVA_OPTS $HOTSWAP_AGENT $REMOTE_DEBUG -jar start.jar > >(tee /dev/stdout_pod) 2> >(tee /dev/stderr_pod)
      '
  logs:
    description: Print logs of the application.
    command: devspace logs -f --pod $HOSTNAME -c invoice-admin
