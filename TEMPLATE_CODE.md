# 代码模板与开发指南

本文档旨在为 `invoice-admin` 项目提供一套标准的开发流程和代码模板，以确保代码质量和开发效率。

## 开发流程

当需要添加一个新的业务功能时（例如，管理“商品”），推荐遵循以下步骤：

1. **定义契约 (`client` 模块)**:
    * 在 `invoice.dto` 包下创建 `GoodsDTO.java`，用于数据展示。
    * 在 `invoice.dto.command` 包下创建 `GoodsCreateCmd.java` 和 `GoodsEditCmd.java`，用于接收创建和编辑的请求参数。
    * 在 `invoice.dto.query` 包下创建 `GoodsPageQry.java`，用于接收分页查询参数。

2. **实现数据访问 (`infrastructure` 模块)**:
    * 在 `invoice.dataobject` 包下创建 `GoodsDO.java`，与数据库表结构对应。
    * 在 `invoice.dao` 包下创建 `GoodsDao.java` 接口，定义数据库操作。
    * 在 `invoice.converter` 包下创建 `GoodsConverter.java` (MapStruct 接口)，用于 `DO`, `DTO`, 和领域对象之间的转换。

3. **编写应用服务 (`app` 模块)**:
    * 在 `invoice` 包下创建 `GoodsAppService.java` 接口。
    * 创建 `GoodsAppServiceImpl.java` 实现类，注入 `GoodsDao` 和 `GoodsConverter`，编排业务逻辑。

4. **暴露 API (`adapter` 模块)**:
    * 在 `api` 包下创建 `GoodsApiController.java`，注入 `GoodsAppService`，并创建对应的 RESTful API 接口。

## 代码模板

以下是基于“业务接入”(`Business`)模块的代码示例，你可以此为模板进行开发。

### 1. Controller (`adapter` 层)

**路径**: `invoice-admin-adapter/src/main/java/com/xtc/marketing/invoiceservice/api/`

```java
// BusinessApiController.java

/**
 * 业务接入接口
 */
@Validated
@RequiredArgsConstructor
@RequestMapping("/api")
@RestController
public class BusinessApiController {

    private final BusinessAppService businessAppService;

    /**
     * 业务接入分页列表
     *
     * @param qry 参数
     * @return 业务接入分页列表
     */
    @GetMapping("/businesses")
    public PageResponse<BusinessDTO> pageBusinesses(@Valid BusinessPageQry qry) {
        return businessAppService.pageBusinesses(qry);
    }

    /**
     * 业务接入详情
     *
     * @param id 业务接入id
     * @return 业务接入详情
     */
    @GetMapping("/businesses/{id}")
    public SingleResponse<BusinessDTO> businessDetail(@NotNull @Positive @PathVariable("id") Long id) {
        BusinessDTO detail = businessAppService.businessDetail(id);
        return SingleResponse.of(detail);
    }

    /**
     * 新增业务接入
     *
     * @param cmd 参数
     * @return 业务接入id
     */
    @PostMapping("/businesses")
    public SingleResponse<Long> createBusiness(@Valid @RequestBody BusinessCreateCmd cmd) {
        Long id = businessAppService.createBusiness(cmd);
        return SingleResponse.of(id);
    }

    /**
     * 编辑业务接入
     *
     * @param id  业务接入id
     * @param cmd 参数
     * @return 执行结果
     */
    @PutMapping("/businesses/{id}")
    public Response editBusiness(@NotNull @Positive @PathVariable("id") Long id,
                                 @Valid @RequestBody BusinessEditCmd cmd) {
        businessAppService.editBusiness(id, cmd);
        return Response.buildSuccess();
    }

    /**
     * 删除业务接入
     *
     * @param id 业务接入id
     * @return 执行结果
     */
    @DeleteMapping("/businesses/{id}")
    public Response removeBusiness(@NotNull @Positive @PathVariable("id") Long id) {
        businessAppService.removeBusiness(id);
        return Response.buildSuccess();
    }

}
```

### 2. Application Service (`app` 层)

**接口路径**: `invoice-admin-app/src/main/java/com/xtc/marketing/invoiceservice/invoice/`

```java
// BusinessAppService.java

public interface BusinessAppService {

    PageResponse<BusinessDTO> pageBusinesses(BusinessPageQry qry);

    BusinessDTO businessDetail(long id);

    Long createBusiness(BusinessCreateCmd cmd);

    void editBusiness(long id, BusinessEditCmd cmd);

    void removeBusiness(long id);

}
```

**实现类路径**: `invoice-admin-app/src/main/java/com/xtc/marketing/invoiceservice/invoice/`

```java
// BusinessAppServiceImpl.java

/**
 * 业务接入应用服务实现类
 * <p>简单逻辑 10 行以内的代码可以直接在 service 方法内实现</p>
 * <p>复杂逻辑（或可复用逻辑）封装在 executor 里实现，调用链路：service > executor</p>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BusinessAppServiceImpl implements BusinessAppService {

    private final BusinessDao businessDao;
    private final BusinessConverter businessConverter;

    @Override
    public Long createBusiness(BusinessCreateCmd cmd) {
        // 1. 校验逻辑
        boolean existsByBizCode = businessDao.existsByBizCode(cmd.getBizCode());
        if (existsByBizCode) {
            throw BizException.of("业务代码已存在");
        }
        // 2. 转换并保存
        BusinessDO businessDO = BeanCopier.copy(cmd, BusinessDO::new);
        businessDao.save(businessDO);
        return businessDO.getId();
    }

    // 其他方法的实现...
}
```

### 3. Command & Query (`client` 层)

**路径**: `invoice-admin-client/src/main/java/com/xtc/marketing/invoiceservice/invoice/dto/command/`

```java
// BusinessCreateCmd.java

@Getter
@Setter
@ToString
public class BusinessCreateCmd {

    /**
     * 业务代码
     */
    @NotBlank
    @Length(max = 50)
    private String bizCode;
    /**
     * 业务名称
     */
    @NotBlank
    @Length(max = 50)
    private String bizName;
    /**
     * 启动标识
     */
    @NotNull
    private Boolean enabled;

}
```

### 4. Data Object (`infrastructure` 层)

**路径**: `invoice-admin-infrastructure/src/main/java/com/xtc/marketing/invoiceservice/invoice/dataobject/`

```java
// BusinessDO.java

/**
 * 业务接入表
 */
@Getter
@Setter
@ToString
@SuperBuilder
@NoArgsConstructor
@Table("t_business")
public class BusinessDO extends BaseDO {

    /**
     * 业务代码
     */
    private String bizCode;
    /**
     * 业务名称
     */
    private String bizName;
    /**
     * 启用标识
     */
    private Boolean enabled;

}
```

### 5. DAO (`infrastructure` 层)

**路径**: `invoice-admin-infrastructure/src/main/java/com/xtc/marketing/invoiceservice/invoice/dao/`

```java
// BusinessDao.java

/**
 * 业务接入数据库访问类
 * <p>原则上不写 sql 语句</p>
 * <p>从模板项目中获取 BaseDao 也可以使用 ServiceImpl</p>
 * <p>参数校验使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.If} 包含常用的：非空、非空集合、非空字符串等</p>
 * <p>SQL 函数使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.QueryMethods}</p>
 */
@Repository
public class BusinessDao extends ServiceImpl<BusinessMapper, BusinessDO> {
    // 可在此处添加自定义的数据库查询方法
}