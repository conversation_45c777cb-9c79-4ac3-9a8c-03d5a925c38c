package com.xtc.marketing.invoiceservice.util;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * 数据脱敏工具
 */
public class MaskingUtil {

    /**
     * 手机号正则
     */
    private static final String PHONE_NUMBER_REGEX = "0?(1)[0-9]{10}";

    private MaskingUtil() {
    }

    /**
     * 手机号脱敏
     *
     * @param phoneNumber 手机号
     * @return 脱敏手机号
     */
    public static String maskingPhoneNumber(String phoneNumber) {
        // 如果是手机号则隐藏中间四位，否则只保留后四位
        if (Pattern.matches(PHONE_NUMBER_REGEX, phoneNumber)) {
            return phoneNumber.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
        } else {
            return "*******" + phoneNumber.substring(phoneNumber.length() - 4);
        }
    }

    /**
     * 姓名脱敏
     *
     * @param name 姓名
     * @return 脱敏姓名
     */
    public static String maskingName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        String maskingName = StringUtils.left(name, 1);
        return StringUtils.rightPad(maskingName, StringUtils.length(name), "*");
    }

    /**
     * 所有字符脱敏
     *
     * @param str 字符串
     * @return 脱敏字符串
     */
    public static String maskingAll(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        return StringUtils.rightPad("", StringUtils.length(str), "*");
    }

}
