package com.xtc.marketing.invoiceservice.exception;

import lombok.Getter;

/**
 * 错误码主要有3部分组成：类型+场景+自定义标识
 */
@Getter
public enum BizErrorCode {
    // 参数异常
//    P_USER_UserIdNotNull("P_USER_UserIdNotNull", "用户id不能为空"),

    // 任务
    B_TASK_TaskImportError("B_TASK_TaskImportError", "导入文件读取失败"),
    // 用户
    B_USER_UserNotExists("B_USER_UserNotExists", "用户不存在"),
    B_USER_UserDisabled("B_USER_UserDisabled", "用户未启用"),
    // 商品
    B_GOODS_GoodsNotExists("B_GOODS_GoodsNotExists", "商品不存在"),
    // 销售方
    B_SELLER_SellerNotExists("B_SELLER_SellerNotExists", "销售方不存在"),
    // 购买方
    B_BUYER_BuyerNotExists("B_BUYER_BuyerNotExists", "购买方不存在"),
    // 业务接入
    B_BUSINESS_BusinessNotExists("B_BUSINESS_BusinessNotExists", "业务不存在"),
    // 发票
    B_INVOICE_InvoiceNotExists("B_INVOICE_InvoiceNotExists", "发票不存在"),
    ;

    private final String errCode;
    private final String errDesc;

    BizErrorCode(String errCode, String errDesc) {
        this.errCode = errCode;
        this.errDesc = errDesc;
    }
}
