<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.xtc.marketing.invoiceservice</groupId>
    <artifactId>invoice-admin-parent</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <name>invoice-admin</name>

    <properties>
        <!--Project-->
        <java.version>21</java.version>
        <project.encoding>UTF-8</project.encoding>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>${project.encoding}</project.build.sourceEncoding>
        <project.reporting.outputEncoding>${project.encoding}</project.reporting.outputEncoding>
        <!--Misc-->
        <druid-starter.version>1.2.24</druid-starter.version>
        <mybatis-flex.version>1.11.0</mybatis-flex.version>
        <redisson-starter.version>3.45.1</redisson-starter.version>
        <arthas-starter.version>4.0.5</arthas-starter.version>
        <lombok.version>1.18.38</lombok.version>
        <mapstruct.version>1.6.3</mapstruct.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <gson.version>2.12.1</gson.version>
        <commons-collections.version>4.5.0-M3</commons-collections.version>
        <commons-io.version>2.18.0</commons-io.version>
        <guava.version>33.4.7-jre</guava.version>
        <fastexcel.version>1.2.0</fastexcel.version>
        <xxl-job.version>3.0.0</xxl-job.version>
        <java-jwt.version>4.5.0</java-jwt.version>
        <jwks-rsa.version>0.22.1</jwks-rsa.version>
        <minio.version>8.5.17</minio.version>
        <!--XTC Client-->
        <invoice-admin-client.version>1.0.0</invoice-admin-client.version>
        <invoice-service-client.version>1.1.0</invoice-service-client.version>
        <!--Framework-->
        <marketing-component-dto.version>2.0.0</marketing-component-dto.version>
        <cola.components.version>5.0.0</cola.components.version>
        <spring-cloud.version>2024.0.1</spring-cloud.version>
        <spring-boot.version>3.4.4</spring-boot.version>
        <start-class>com.xtc.marketing.invoiceservice.Application</start-class>
    </properties>

    <modules>
        <module>invoice-admin-client</module>
        <module>invoice-admin-adapter</module>
        <module>invoice-admin-app</module>
        <module>invoice-admin-domain</module>
        <module>invoice-admin-infrastructure</module>
        <module>invoice-admin-common</module>
        <module>start</module>
    </modules>

    <repositories>
        <!--阿里云镜像库-->
        <repository>
            <id>alimaven</id>
            <name>aliyun maven</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <layout>default</layout>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <!--公司内部库-->
        <repository>
            <id>maven-public</id>
            <url>http://packages.bbkedu.com/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </releases>
        </repository>
    </repositories>

    <dependencies>
        <!--Hot reload agent-->
        <dependency>
            <groupId>org.hotswapagent</groupId>
            <artifactId>hotswap-agent-spring-boot-plugin</artifactId>
            <version>2.0.1</version>
            <scope>provided</scope>
        </dependency>
        <!--Hot reload agent End-->
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!--Project modules-->
            <dependency>
                <groupId>com.xtc.marketing.invoiceservice</groupId>
                <artifactId>invoice-admin-adapter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xtc.marketing.invoiceservice</groupId>
                <artifactId>invoice-admin-client</artifactId>
                <version>${invoice-admin-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xtc.marketing.invoiceservice</groupId>
                <artifactId>invoice-admin-app</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xtc.marketing.invoiceservice</groupId>
                <artifactId>invoice-admin-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xtc.marketing.invoiceservice</groupId>
                <artifactId>invoice-admin-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xtc.marketing.invoiceservice</groupId>
                <artifactId>invoice-admin-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!--Project modules End-->

            <!--COLA Components-->
            <dependency>
                <groupId>com.alibaba.cola</groupId>
                <artifactId>cola-components-bom</artifactId>
                <version>${cola.components.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--COLA Components End-->

            <!--Marketing Components-->
            <dependency>
                <groupId>com.xtc.marketing.marketingcomponents</groupId>
                <artifactId>marketing-component-dto</artifactId>
                <version>${marketing-component-dto.version}</version>
            </dependency>
            <!--Marketing Components End-->

            <!--Spring Cloud-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--Spring Cloud End-->

            <!--Spring Boot-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>${druid-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mybatis-flex</groupId>
                <artifactId>mybatis-flex-dependencies</artifactId>
                <version>${mybatis-flex.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.arthas</groupId>
                <artifactId>arthas-spring-boot-starter</artifactId>
                <version>${arthas-starter.version}</version>
            </dependency>
            <!--Spring Boot End-->

            <!--XTC Client-->
            <dependency>
                <groupId>com.xtc.marketing.invoiceservice</groupId>
                <artifactId>invoice-service-client</artifactId>
                <version>${invoice-service-client.version}</version>
            </dependency>
            <!--XTC Client End-->

            <!--Misc-->
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.idev.excel</groupId>
                <artifactId>fastexcel</artifactId>
                <version>${fastexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${java-jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>jwks-rsa</artifactId>
                <version>${jwks-rsa.version}</version>
            </dependency>
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>
            <!--Misc End-->
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.3.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.14.0</version>
                    <configuration>
                        <annotationProcessorPaths>
                            <!--必须要添加 lombok 的 path，解决和 mapstruct 的冲突-->
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok-mapstruct-binding</artifactId>
                                <version>${lombok-mapstruct-binding.version}</version>
                            </path>
                            <path>
                                <groupId>com.mybatis-flex</groupId>
                                <artifactId>mybatis-flex-processor</artifactId>
                                <version>${mybatis-flex.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.3.1</version>
                    <!--发布源码-->
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <phase>package</phase>
                            <goals>
                                <goal>jar-no-fork</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.11.2</version>
                </plugin>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>3.1.4</version>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <configuration>
                        <mainClass>${start-class}</mainClass>
                        <includeSystemScope>true</includeSystemScope>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <!--    <distributionManagement>-->
    <!--        <repository>-->
    <!--            <id>maven-releases</id>-->
    <!--            <name>maven-releases</name>-->
    <!--            <url>http://packages.bbkedu.com/repository/maven-releases/</url>-->
    <!--        </repository>-->
    <!--        <snapshotRepository>-->
    <!--            <id>maven-snapshots</id>-->
    <!--            <name>maven-snapshots</name>-->
    <!--            <url>http://packages.bbkedu.com/repository/maven-snapshots/</url>-->
    <!--        </snapshotRepository>-->
    <!--    </distributionManagement>-->
</project>