# 发票管理后台 (invoice-admin)

## 1. 项目简介

本系统是发票管理后台服务，提供对发票相关业务（如业务接入、销方、购方、商品、发票等）的统一管理功能。

项目采用现代化的 Java 技术栈和领域驱动设计（DDD）分层架构，确保了代码的清晰性、可维护性和可扩展性。

## 2. 技术栈

- **后端框架**: SpringBoot3
- **数据持久化**: MyBatis-Flex
- **构建工具**: Maven
- **语言**: Java21
- **辅助工具**:
    - Lombok: 简化实体类和样板代码。
    - MapStruct: 高性能的 Java Bean 映射工具。
    - Jakarta Bean Validation: 提供标准的数据校验 API。

## 3. 架构设计

项目遵循领域驱动设计（DDD）的分层架构思想，将系统划分为明确的层次，各层职责分明。

```
+------------------------------------------------+
|               Adapter Layer (适配层)            |
| (Controllers, Interceptors, Filters)           |
+------------------------------------------------+
|                App Layer (应用层)               |
| (Application Services, DTOs)                   |
+------------------------------------------------+
|               Domain Layer (不使用)             |
| (Entities, Value Objects, Domain Services)     |
+------------------------------------------------+
|           Infrastructure Layer (基础设施层)      |
| (DAOs, Repositories, External Services)        |
+------------------------------------------------+
```

### 模块说明

- `invoice-admin-adapter`: **适配层**。负责处理外部世界的交互，主要是 RESTful API 的暴露。它依赖于应用层。
- `invoice-admin-app`: **应用层**。负责编排领域服务，实现具体的业务用例。它是领域层和基础设施层的协调者。
- `invoice-admin-client`: **客户端**。定义了对外暴露的数据契约，包括数据传输对象（DTO）、命令（Command）和查询（Query）。
- `invoice-admin-domain`: **领域层**。不使用。
- `invoice-admin-infrastructure`: **基础设施层**。提供通用的技术能力，如数据库访问（DAO）、缓存、RPC 调用等。
- `start`: **启动模块**。包含 `main` 方法，负责应用的组装和启动。

## 4. 如何运行

1. **构建项目**:
   在项目根目录下执行 Maven 命令进行编译和打包。
   ```bash
   mvn clean install
   ```

2. **运行项目**:
   构建成功后，在 `start` 模块的 `target` 目录下会生成可执行的 jar 包。
   ```bash
   java -jar start/target/invoice-admin-start-1.0.0.jar
   ```

## 5. API 示例

以下是“业务接入”模块的一个 API 示例。

**Controller: `BusinessApiController.java`**

```java

@Validated
@RequiredArgsConstructor
@RequestMapping("/api")
@RestController
public class BusinessApiController {

    private final BusinessAppService businessAppService;

    /**
     * 业务接入分页列表
     */
    @GetMapping("/businesses")
    public PageResponse<BusinessDTO> pageBusinesses(@Valid BusinessPageQry qry) {
        return businessAppService.pageBusinesses(qry);
    }

    /**
     * 新增业务接入
     */
    @PostMapping("/businesses")
    public SingleResponse<Long> createBusiness(@Valid @RequestBody BusinessCreateCmd cmd) {
        Long id = businessAppService.createBusiness(cmd);
        return SingleResponse.of(id);
    }

}
```

## 6. 开发规范与模板

为了保持代码风格的统一并提高开发效率，我们整理了一份开发指南和代码模板。请参考：
[**代码模板与开发指南 (TEMPLATE_CODE.md)**](./TEMPLATE_CODE.md)