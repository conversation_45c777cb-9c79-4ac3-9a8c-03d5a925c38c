package com.xtc.marketing.invoiceservice.rpc.erp.erpdto.request;

import com.google.gson.annotations.SerializedName;
import com.xtc.marketing.invoiceservice.rpc.erp.erpdto.ErpPageRequest;
import com.xtc.marketing.invoiceservice.rpc.erp.erpdto.response.InvoiceGoodsResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.http.HttpMethod;

import java.time.LocalDateTime;

/**
 * 获取物料开票信息分页请求
 */
@Getter
@Setter
@ToString
public class InvoiceGoodsPageRequest extends ErpPageRequest<InvoiceGoodsResponse> {

    /**
     * 最后更新时间开始
     */
    @SerializedName(value = "lastupdatedatefrom", alternate = "lastUpdateDateFrom")
    private LocalDateTime lastUpdateDateFrom;
    /**
     * 最后更新时间结束
     */
    @SerializedName(value = "lastupdatedateto", alternate = "lastUpdateDateTo")
    private LocalDateTime lastUpdateDateTo;

    @Override
    public String getApiPath() {
        return "/api/erpQuery/getItemInvoiceInfo";
    }

    @Override
    public HttpMethod getRequestMethod() {
        return HttpMethod.GET;
    }

    @Override
    public Class<InvoiceGoodsResponse> getResponseClass() {
        return InvoiceGoodsResponse.class;
    }

}
