package com.xtc.marketing.invoiceservice.rpc.xtcitauth;

import com.xtc.marketing.invoiceservice.constant.SystemConstant;
import com.xtc.marketing.invoiceservice.exception.SysErrorCode;
import com.xtc.marketing.invoiceservice.exception.SysException;
import com.xtc.marketing.invoiceservice.rpc.xtcitauth.xtcitauthdto.AuthBaseResponse;
import com.xtc.marketing.invoiceservice.rpc.xtcitauth.xtcitauthdto.UserDTO;
import com.xtc.marketing.invoiceservice.util.GsonUtil;
import com.xtc.marketing.invoiceservice.util.HttpUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 小天才IT鉴权RPC
 */
@Component
public class XtcItAuthRpc {

    /* RPC配置 */
    /**
     * 测试环境域名
     */
    private static final String TEST_DOMAIN = "https://xtc-gateway-test.okii.com";
    /**
     * 正式环境域名
     * 内网sso：xtc-gateway.okii.com
     * 精灵sso：jl-gateway.okii.com
     */
    private static final String DOMAIN = "https://xtc-gateway.okii.com";
    /**
     * 请求头：xtc-app-id
     */
    private static final String HEADER_XTC_APP_ID = "xtc-app-id";
    /**
     * 请求头：Authorization
     */
    private static final String HEADER_AUTHORIZATION = "Authorization";
    /**
     * 请求appId
     */
    private static final String APP_ID = "xtc-chengda";

    /* 接口 */
    /**
     * 查询用户数据
     */
    private static final String API_GET_USER_INFO = "/authorization-center/auth/getUserInfo";

    @Value("${spring.profiles.active}")
    private String profileActive;

    /**
     * 查询用户数据
     *
     * @param token 用户token
     * @return 用户数据
     */
    public UserDTO getUser(String token) {
        String domain = SystemConstant.isTestProfile(profileActive) ? TEST_DOMAIN : DOMAIN;
        String url = domain + API_GET_USER_INFO;
        Map<String, String> headers = new HashMap<>();
        headers.put(HEADER_XTC_APP_ID, APP_ID);
        headers.put(HEADER_AUTHORIZATION, token);
        String responseStr = "";
        try {
            responseStr = HttpUtil.get(url, headers);
            AuthBaseResponse<UserDTO> response = GsonUtil.jsonToBean(responseStr, AuthBaseResponse.class, UserDTO.class);
            return response.getData();
        } catch (Exception e) {
            String msg = String.format("小天才IT鉴权RPC异常 response: %s, url: %s", responseStr, url);
            throw SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg, e);
        }
    }

    public static void main(String[] args) {
        XtcItAuthRpc rpc = new XtcItAuthRpc();
        rpc.profileActive = "dev";
        String token = "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
        rpc.getUser(token);
        rpc.profileActive = "prod";
        String prodToken = "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
        rpc.getUser(prodToken);
        String expireToken = "Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
        rpc.getUser(expireToken);
    }

}
