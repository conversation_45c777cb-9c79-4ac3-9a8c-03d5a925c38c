package com.xtc.marketing.invoiceservice.invoice.dao;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.If;
import com.xtc.marketing.invoiceservice.config.BaseDao;
import com.xtc.marketing.invoiceservice.invoice.dao.mapper.InvoiceApplyMapper;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceApplyDO;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceApplyPageQry;
import com.xtc.marketing.invoiceservice.util.DateUtil;
import org.springframework.stereotype.Repository;

import static com.xtc.marketing.invoiceservice.invoice.dataobject.table.InvoiceApplyDOTableDef.INVOICE_APPLY_DO;

/**
 * 发票申请数据库访问类
 * <p>原则上不写 sql 语句</p>
 * <p>从模板项目中获取 BaseDao 也可以使用 ServiceImpl</p>
 * <p>参数校验使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.If} 包含常用的：非空、非空集合、非空字符串等</p>
 * <p>SQL 函数使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.QueryMethods}</p>
 */
@Repository
public class InvoiceApplyDao extends BaseDao<InvoiceApplyMapper, InvoiceApplyDO> {

    /**
     * 查询发票申请分页列表
     *
     * @param qry 参数
     * @return 发票申请分页列表
     */
    public Page<InvoiceApplyDO> pageBy(InvoiceApplyPageQry qry) {
        Page<InvoiceApplyDO> page = Page.of(qry.getPageIndex(), qry.getPageSize());
        return queryChain()
                .where(INVOICE_APPLY_DO.BIZ_CODE.eq(qry.getBizCode(), If::hasText))
                .and(INVOICE_APPLY_DO.APPLY_ID.eq(qry.getApplyId(), If::hasText))
                .and(INVOICE_APPLY_DO.INVOICE_ID.eq(qry.getInvoiceId(), If::hasText))
                .and(INVOICE_APPLY_DO.BIZ_ORDER_ID.eq(qry.getBizOrderId(), If::hasText))
                .and(INVOICE_APPLY_DO.SERIAL_NO.eq(qry.getSerialNo(), If::hasText))
                .and(INVOICE_APPLY_DO.BUYER_IDENTIFY_NO.eq(qry.getBuyerIdentifyNo(), If::hasText))
                .and(INVOICE_APPLY_DO.CREATE_TIME.between(DateUtil.startOfDay(qry.getCreateDateStart()), DateUtil.endOfDay(qry.getCreateDateEnd()),
                        If.notNull(qry.getCreateDateStart()) && If.notNull(qry.getCreateDateEnd())))
                .orderBy(INVOICE_APPLY_DO.CREATE_TIME.desc())
                .page(page);
    }

}
