package com.xtc.marketing.invoiceservice.invoice.dataobject;

import com.mybatisflex.annotation.Table;
import com.xtc.marketing.invoiceservice.config.BaseDO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * 购买方表
 */
@Getter
@Setter
@ToString
@SuperBuilder
@NoArgsConstructor
@Table("t_buyer")
public class BuyerDO extends BaseDO {

    /**
     * 购买方代码
     */
    private String buyerCode;
    /**
     * 购买方税号
     */
    private String buyerIdentifyNo;
    /**
     * 购买方名称
     */
    private String buyerName;
    /**
     * 购买方电话
     */
    private String buyerPhone;
    /**
     * 购买方银行
     */
    private String buyerBankName;
    /**
     * 购买方银行账号
     */
    private String buyerBankAccount;
    /**
     * 购买方地址
     */
    private String buyerAddress;
    /**
     * 启用标识
     */
    private Boolean enabled;

}
