package com.xtc.marketing.invoiceservice.invoice.dataobject;

import com.mybatisflex.annotation.Table;
import com.xtc.marketing.invoiceservice.config.BaseDO;
import com.xtc.marketing.invoiceservice.invoice.enums.InvoiceItemTypeEnum;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

/**
 * 发票项目表
 */
@Getter
@Setter
@ToString
@SuperBuilder
@NoArgsConstructor
@Table("t_invoice_item")
public class InvoiceItemDO extends BaseDO {

    /**
     * 发票id
     */
    private String invoiceId;
    /**
     * 开票流水号
     */
    private String serialNo;
    /**
     * 发票号码
     */
    private String invoiceNo;
    /**
     * 发票行号
     */
    private Integer itemNo;
    /**
     * 发票项目类型
     */
    private InvoiceItemTypeEnum itemType;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 税收分类编码
     */
    private String taxClassificationCode;
    /**
     * 规格型号
     */
    private String specification;
    /**
     * 单位
     */
    private String unit;
    /**
     * 数量
     */
    private Integer num;
    /**
     * 单价（单位：分）
     */
    private Integer unitPrice;
    /**
     * 总价（单位：分）
     */
    private Integer totalPrice;
    /**
     * 税率（13%=0.13）
     */
    private BigDecimal taxRate;
    /**
     * 税额（单位：分）
     */
    private Integer taxAmount;

}
