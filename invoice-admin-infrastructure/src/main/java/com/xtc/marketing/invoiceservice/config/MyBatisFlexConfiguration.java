package com.xtc.marketing.invoiceservice.config;

import com.mybatisflex.annotation.InsertListener;
import com.mybatisflex.annotation.UpdateListener;
import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.core.dialect.IDialect;
import com.mybatisflex.core.logicdelete.LogicDeleteProcessor;
import com.mybatisflex.core.logicdelete.impl.BooleanLogicDeleteProcessor;
import com.mybatisflex.core.table.ColumnInfo;
import com.mybatisflex.core.table.TableInfo;
import com.mybatisflex.core.tenant.TenantFactory;
import com.mybatisflex.core.tenant.TenantManager;
import com.xtc.marketing.invoiceservice.context.UserContext;
import com.xtc.marketing.invoiceservice.util.GsonUtil;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;

import static com.mybatisflex.core.constant.SqlConsts.EQUALS;

/**
 * MyBatis-Flex 全局配置
 */
@Configuration
public class MyBatisFlexConfiguration {

    /**
     * 不限制租户
     */
    private static final Object[] TENANT_EMPTY = {};
    /**
     * 未启用租户
     */
    private static final Object[] TENANT_BIZ_DISABLED = {"BIZ_DISABLED"};

    MyBatisFlexConfiguration() {
        FlexGlobalConfig config = FlexGlobalConfig.getDefaultConfig();
        // 注册操作人数据监听器
        OperatorListener operatorListener = new OperatorListener();
        config.registerInsertListener(operatorListener, BaseDO.class);
        config.registerUpdateListener(operatorListener, BaseDO.class);
        // 设置多租户逻辑
        TenantManager.setTenantFactory(this.createTenantFactory());
    }

    /**
     * 创建逻辑删除处理器
     * <p>记录删除人和删除时间</p>
     *
     * @return 逻辑删除处理器
     */
    @Bean
    public LogicDeleteProcessor logicDeleteProcessor() {
        return new BooleanLogicDeleteProcessor() {

            /**
             * 数据列：删除人
             */
            public static final String COLUMN_UPDATE_BY = "update_by";
            /**
             * 数据列：删除时间
             */
            public static final String COLUMN_UPDATE_TIME = "update_time";

            @Override
            public String buildLogicDeletedSet(String logicColumn, TableInfo tableInfo, IDialect dialect) {
                // 默认逻辑删除的 sql 语句
                String sql = dialect.wrap(logicColumn) + EQUALS + getLogicDeletedValue();
                // 获取当前用户
                UserContext.User user = UserContext.getUser();
                if (user == null) {
                    return sql;
                }
                // 扩展逻辑删除的 sql 语句，增加删除人和删除时间
                StringBuilder sqlBuilder = new StringBuilder(sql);
                for (ColumnInfo columnInfo : tableInfo.getColumnInfoList()) {
                    if (columnInfo.getColumn().equalsIgnoreCase(COLUMN_UPDATE_BY)) {
                        BaseDO.Operator operator = BaseDO.Operator.of(user.getEmployeeId(), user.getUserName());
                        String updateBySql = ", %s %s '%s'".formatted(dialect.wrap(COLUMN_UPDATE_BY), EQUALS, GsonUtil.objectToJson(operator));
                        sqlBuilder.append(updateBySql);
                    }
                    if (columnInfo.getColumn().equalsIgnoreCase(COLUMN_UPDATE_TIME)) {
                        String updateTimeSql = ", %s %s now()".formatted(dialect.wrap(COLUMN_UPDATE_TIME), EQUALS);
                        sqlBuilder.append(updateTimeSql);
                    }
                }
                return sqlBuilder.toString();
            }
        };
    }

    /**
     * 操作人数据监听器，用于设置创建人和更新人
     */
    private static class OperatorListener implements InsertListener, UpdateListener {

        @Override
        public void onInsert(Object entity) {
            UserContext.User user = UserContext.getUser();
            if (user == null) {
                return;
            }
            BaseDO.Operator operator = BaseDO.Operator.of(user.getEmployeeId(), user.getUserName());
            if (entity instanceof BaseDO baseDO) {
                LocalDateTime now = LocalDateTime.now();
                baseDO.setCreateBy(operator);
                baseDO.setCreateTime(now);
                baseDO.setUpdateBy(operator);
                baseDO.setUpdateTime(now);
            }
        }

        @Override
        public void onUpdate(Object entity) {
            UserContext.User user = UserContext.getUser();
            if (user == null) {
                return;
            }
            BaseDO.Operator operator = BaseDO.Operator.of(user.getEmployeeId(), user.getUserName());
            if (entity instanceof BaseDO baseDO) {
                baseDO.setUpdateBy(operator);
                baseDO.setUpdateTime(LocalDateTime.now());
            }
        }

    }

    /**
     * 创建租户工厂
     *
     * @return 租户工厂
     */
    private TenantFactory createTenantFactory() {
        return () -> {
            UserContext.User user = UserContext.getUser();
            if (user == null) {
                return TENANT_EMPTY;
            }
            // 查询用户对应的业务
            if (StringUtils.isBlank(user.getBizCode()) || user.getBusiness() == null) {
                return TENANT_EMPTY;
            }
            // 判断业务未启用
            if (BooleanUtils.isNotTrue(user.getBusiness().getEnabled())) {
                return TENANT_BIZ_DISABLED;
            }
            // 返回正确的租户
            return new Object[]{user.getBizCode()};
        };
    }

}