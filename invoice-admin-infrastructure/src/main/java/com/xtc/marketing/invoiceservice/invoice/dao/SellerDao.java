package com.xtc.marketing.invoiceservice.invoice.dao;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.If;
import com.mybatisflex.core.query.QueryCondition;
import com.xtc.marketing.invoiceservice.config.BaseDao;
import com.xtc.marketing.invoiceservice.invoice.dao.mapper.SellerMapper;
import com.xtc.marketing.invoiceservice.invoice.dataobject.SellerDO;
import com.xtc.marketing.invoiceservice.invoice.dto.query.SellerPageQry;
import org.springframework.stereotype.Repository;

import static com.xtc.marketing.invoiceservice.invoice.dataobject.table.SellerDOTableDef.SELLER_DO;

/**
 * 销售方数据库访问类
 * <p>原则上不写 sql 语句</p>
 * <p>从模板项目中获取 BaseDao 也可以使用 ServiceImpl</p>
 * <p>参数校验使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.If} 包含常用的：非空、非空集合、非空字符串等</p>
 * <p>SQL 函数使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.QueryMethods}</p>
 */
@Repository
public class SellerDao extends BaseDao<SellerMapper, SellerDO> {

    /**
     * 查询销售方分页列表
     *
     * @param qry 参数
     * @return 销售方分页列表
     */
    public Page<SellerDO> pageBy(SellerPageQry qry) {
        Page<SellerDO> page = Page.of(qry.getPageIndex(), qry.getPageSize());
        return queryChain()
                .where(SELLER_DO.SELLER_CODE.eq(qry.getSellerCode(), If::hasText))
                .and(SELLER_DO.SELLER_IDENTIFY_NO.eq(qry.getSellerIdentifyNo(), If::hasText))
                .and(
                        QueryCondition.createEmpty()
                                .and("MATCH (" + SELLER_DO.SELLER_NAME.getName() + ") AGAINST (? IN BOOLEAN MODE)", "+" + qry.getSellerName())
                                .when(If.hasText(qry.getSellerName()))
                )
                .and(SELLER_DO.ENABLED.eq(qry.getEnabled(), If::notNull))
                .orderBy(SELLER_DO.ID.desc())
                .page(page);
    }

    /**
     * 判断销售方存在
     *
     * @param sellerCode 销售方代码
     * @return 执行结果
     */
    public boolean existsBySellerCode(String sellerCode) {
        if (If.noText(sellerCode)) {
            return false;
        }
        return queryChain().where(SELLER_DO.SELLER_CODE.eq(sellerCode)).exists();
    }

    /**
     * 判断销售方存在，并排除当前销售方id
     *
     * @param sellerCode 销售方代码
     * @param excludeId  销售方id
     * @return 执行结果
     */
    public boolean existsByCodeExcludeId(String sellerCode, Long excludeId) {
        if (If.noText(sellerCode) || If.isNull(excludeId)) {
            return false;
        }
        return queryChain()
                .where(SELLER_DO.SELLER_CODE.eq(sellerCode))
                .and(SELLER_DO.ID.ne(excludeId))
                .exists();
    }

}
