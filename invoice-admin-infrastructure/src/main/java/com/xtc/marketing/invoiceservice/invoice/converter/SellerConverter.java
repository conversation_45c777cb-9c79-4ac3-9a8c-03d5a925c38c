package com.xtc.marketing.invoiceservice.invoice.converter;

import com.xtc.marketing.invoiceservice.invoice.dataobject.SellerDO;
import com.xtc.marketing.invoiceservice.invoice.dto.SellerDTO;
import org.mapstruct.*;

import java.util.List;

/**
 * 销售方数据转换器
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        // 忽略未设置映射的字段的告警
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface SellerConverter {

    SellerDTO doToDto(SellerDO source);

    List<SellerDTO> doToDto(List<SellerDO> source);

}
