package com.xtc.marketing.invoiceservice.oss;

import com.google.common.collect.Lists;
import com.xtc.marketing.invoiceservice.constant.SystemConstant;
import com.xtc.marketing.invoiceservice.exception.SysErrorCode;
import com.xtc.marketing.invoiceservice.exception.SysException;
import io.minio.*;
import io.minio.messages.Item;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.util.List;

/**
 * 业务文件存储客户端
 * <p><a href="https://min.io/docs/minio/linux/developers/java/API.html">minio API</a></p>
 */
@Slf4j
@Component
public class BizFileOssClient {

    /**
     * 存储桶：正式环境
     */
    private static final String BUCKET_PROD = "marketing-invoice";
    /**
     * 存储桶：测试环境
     */
    private static final String BUCKET_TEST = "marketing-invoice-test";

    /**
     * Minio 客户端
     */
    private MinioClient minioClient;
    /**
     * 存储桶
     */
    private String bucket;

    @Value("${minio.endpoint}")
    private String endpoint;
    @Value("${minio.access-key}")
    private String accessKey;
    @Value("${minio.secret-key}")
    private String secretKey;
    @Value("${spring.profiles.active}")
    private String activeProfile;

    @PostConstruct
    public void init() {
        minioClient = MinioClient.builder()
                .endpoint(endpoint)
                .credentials(accessKey, secretKey)
                .build();
        bucket = SystemConstant.isTestProfile(activeProfile) ? BUCKET_TEST : BUCKET_PROD;
    }

    /**
     * 文件上传
     *
     * @param objectName  对象名
     * @param fileBytes   文件字节数组
     * @param contentType 文件类型
     */
    public void putObject(String objectName, byte[] fileBytes, String contentType) {
        try (BufferedInputStream inputStream = new BufferedInputStream(new ByteArrayInputStream(fileBytes))) {
            putObject(objectName, inputStream, contentType);
        } catch (IOException e) {
            throw SysException.of(SysErrorCode.S_OSS_ERROR, e.getCause());
        }
    }

    /**
     * 文件上传
     *
     * @param objectName  对象名
     * @param file        文件
     * @param contentType 文件类型
     */
    public void putObject(String objectName, File file, String contentType) {
        try {
            putObject(objectName, new BufferedInputStream(new FileInputStream(file)), contentType);
        } catch (FileNotFoundException e) {
            throw SysException.of(SysErrorCode.S_OSS_ERROR, e.getCause());
        }
    }

    /**
     * 文件上传
     * <p><a href="https://www.iana.org/assignments/media-types/media-types.xhtml">CONTENT-TYPE with the desired content type (also called a media type)</a></p>
     * <p>{@link org.springframework.http.MediaType}</p>
     *
     * @param objectName  对象名
     * @param stream      文件流
     * @param contentType 文件类型
     */
    public void putObject(String objectName, InputStream stream, String contentType) {
        try {
            PutObjectArgs.Builder putObjectArgsBuilder = PutObjectArgs.builder()
                    .bucket(bucket)
                    .object(objectName)
                    .stream(stream, stream.available(), -1);
            if (contentType != null) {
                putObjectArgsBuilder.contentType(contentType);
            }
            minioClient.putObject(putObjectArgsBuilder.build());
        } catch (Exception e) {
            throw SysException.of(SysErrorCode.S_OSS_ERROR, e);
        }
    }

    /**
     * 获取文件
     * <p>文件下载接口开发：</p>
     * <pre>{@code
     * public ResponseEntity<Resource> downloadFile(String objectName) {
     *     // 文件名取 objectName 的最后一部分
     *     String[] objectNameSplit = objectName.split("/");
     *     String fileName = objectNameSplit[objectNameSplit.length - 1];
     *     // 空格在浏览器中会被解析成 + 号，所以需要替换成 %20
     *     String encodeFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replace("+", "%20");
     *     String fileNameContentDisposition = "attachment;filename*=utf-8''" + encodeFileName;
     *     // 封装文件流，与 MinIO 的连接以及流的获取只有在实际需要传输数据时才会发生，而不是在请求一开始就建立连接。
     *     GetObjectArgs getObjectArgs = GetObjectArgs.builder().bucket(bucket).object(objectName).build();
     *     InputStreamSource streamSource = () -> minioClient.getObject(getObjectArgs);
     *     Resource resource = new InputStreamResource(streamSource);
     *     // Spring 负责关闭流
     *     return ResponseEntity.ok()
     *             .header("Content-Disposition", fileNameContentDisposition)
     *             .header("Content-Type", MediaType.APPLICATION_OCTET_STREAM_VALUE)
     *             .body(resource);
     * }
     * }</pre>
     *
     * @param objectName 对象名
     * @return 文件流
     * @see org.springframework.http.ResponseEntity
     * @see org.springframework.core.io.Resource
     * @see org.springframework.core.io.InputStreamSource
     * @see org.springframework.http.MediaType
     */
    public InputStream getObject(String objectName) {
        try {
            GetObjectArgs getObjectArgs = GetObjectArgs.builder().bucket(bucket).object(objectName).build();
            return minioClient.getObject(getObjectArgs);
        } catch (Exception e) {
            throw SysException.of(SysErrorCode.S_OSS_ERROR, e);
        }
    }

    /**
     * 删除文件
     *
     * @param objectName 对象名
     */
    public void removeObject(String objectName) {
        try {
            RemoveObjectArgs args = RemoveObjectArgs.builder().bucket(bucket).object(objectName).build();
            minioClient.removeObject(args);
        } catch (Exception e) {
            throw SysException.of(SysErrorCode.S_OSS_ERROR, e);
        }
    }

    /**
     * 查询文件夹的文件列表
     *
     * @param folder  文件夹
     * @param maxKeys 最大查询数量
     * @return 文件列表
     */
    public List<String> listFolderObjects(String folder, int maxKeys) {
        // 限制单次查询数量，不递归查找文件夹
        ListObjectsArgs args = ListObjectsArgs.builder().bucket(bucket).prefix(folder).maxKeys(maxKeys).recursive(false).build();
        Iterable<Result<Item>> resultItems = minioClient.listObjects(args);
        // 由于是懒加载，要做好数量限制，不能直接遍历 Result<Item> 否则会查询所有文件
        List<String> objects = Lists.newArrayListWithCapacity(maxKeys);
        for (Result<Item> resultItem : resultItems) {
            if (objects.size() >= maxKeys) {
                break;
            }
            try {
                objects.add(resultItem.get().objectName());
            } catch (Exception e) {
                log.warn("Failed to get object from MinIO: {}", e.getMessage(), e);
            }
        }
        return objects;
    }

}
