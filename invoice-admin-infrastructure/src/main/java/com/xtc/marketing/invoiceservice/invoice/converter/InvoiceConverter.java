package com.xtc.marketing.invoiceservice.invoice.converter;

import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceApplyDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceDO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceApplyDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceDTO;
import org.mapstruct.*;

import java.util.List;

/**
 * 发票数据转换器
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        // 忽略未设置映射的字段的告警
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface InvoiceConverter {

    List<InvoiceApplyDTO> toInvoiceApplyDTO(List<InvoiceApplyDO> source);

    List<InvoiceDTO> toInvoiceDTO(List<InvoiceDO> records);

}
