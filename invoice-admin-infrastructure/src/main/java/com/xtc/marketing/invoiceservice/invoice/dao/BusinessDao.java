package com.xtc.marketing.invoiceservice.invoice.dao;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.If;
import com.mybatisflex.core.query.QueryCondition;
import com.xtc.marketing.invoiceservice.config.BaseDao;
import com.xtc.marketing.invoiceservice.invoice.dao.mapper.BusinessMapper;
import com.xtc.marketing.invoiceservice.invoice.dataobject.BusinessDO;
import com.xtc.marketing.invoiceservice.invoice.dto.query.BusinessPageQry;
import org.springframework.stereotype.Repository;

import java.util.Optional;

import static com.xtc.marketing.invoiceservice.invoice.dataobject.table.BusinessDOTableDef.BUSINESS_DO;

/**
 * 业务接入数据库访问类
 * <p>原则上不写 sql 语句</p>
 * <p>从模板项目中获取 BaseDao 也可以使用 ServiceImpl</p>
 * <p>参数校验使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.If} 包含常用的：非空、非空集合、非空字符串等</p>
 * <p>SQL 函数使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.QueryMethods}</p>
 */
@Repository
public class BusinessDao extends BaseDao<BusinessMapper, BusinessDO> {

    /**
     * 查询业务接入分页列表
     *
     * @param qry 参数
     * @return 业务接入分页列表
     */
    public Page<BusinessDO> pageBy(BusinessPageQry qry) {
        Page<BusinessDO> page = Page.of(qry.getPageIndex(), qry.getPageSize());
        return queryChain()
                .where(BUSINESS_DO.BIZ_CODE.eq(qry.getBizCode(), If::hasText))
                .and(
                        QueryCondition.createEmpty()
                                .and("MATCH (" + BUSINESS_DO.BIZ_NAME.getName() + ") AGAINST (? IN BOOLEAN MODE)", "+" + qry.getBizName())
                                .when(If.hasText(qry.getBizName()))
                )
                .and(BUSINESS_DO.ENABLED.eq(qry.getEnabled(), If::notNull))
                .orderBy(BUSINESS_DO.ID.desc())
                .page(page);
    }

    /**
     * 查询业务接入
     *
     * @param bizCode 业务代码
     * @return 业务接入
     */
    public Optional<BusinessDO> getByBizCode(String bizCode) {
        if (If.noText(bizCode)) {
            return Optional.empty();
        }
        return queryChain()
                .where(BUSINESS_DO.BIZ_CODE.eq(bizCode))
                .orderBy(BUSINESS_DO.ID.desc())
                .limit(LIMIT_ONE)
                .oneOpt();
    }

    /**
     * 判断业务接入存在
     *
     * @param bizCode 业务代码
     * @return 执行结果
     */
    public boolean existsByBizCode(String bizCode) {
        if (If.noText(bizCode)) {
            return false;
        }
        return queryChain().where(BUSINESS_DO.BIZ_CODE.eq(bizCode)).exists();
    }

    /**
     * 判断业务接入存在（排除指定id）
     *
     * @param bizCode 业务代码
     * @param id      业务接入id
     * @return 执行结果
     */
    public boolean existsByCodeExcludeId(String bizCode, Long id) {
        if (If.noText(bizCode)) {
            return false;
        }
        return queryChain()
                .where(BUSINESS_DO.BIZ_CODE.eq(bizCode))
                .and(BUSINESS_DO.ID.ne(id))
                .exists();
    }

}
