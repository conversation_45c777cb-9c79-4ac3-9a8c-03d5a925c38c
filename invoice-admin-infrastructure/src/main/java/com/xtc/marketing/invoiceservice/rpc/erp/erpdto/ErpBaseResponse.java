package com.xtc.marketing.invoiceservice.rpc.erp.erpdto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * ERP系统响应基类
 */
@Getter
@Setter
@ToString
public class ErpBaseResponse<T> {

    /**
     * 成功代码
     */
    private static final String SUCCESS_CODE = "S";

    /**
     * 响应码
     */
    private String status;
    /**
     * 响应描述
     */
    private String msg;
    /**
     * 总页数
     */
    private Integer totalPages;
    /**
     * 数据总数
     */
    private Integer totalRows;
    /**
     * 页码
     */
    private Integer currentPage;
    /**
     * 数据数量
     */
    private Integer pageSize;

    /**
     * 判断响应成功
     *
     * @return 执行结果
     */
    public boolean isSuccess() {
        return !isFailure();
    }

    /**
     * 判断响应失败
     *
     * @return 执行结果
     */
    public boolean isFailure() {
        return !SUCCESS_CODE.equals(this.status);
    }

}
