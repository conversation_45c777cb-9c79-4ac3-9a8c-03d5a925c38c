package com.xtc.marketing.invoiceservice.invoice.dao;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.If;
import com.mybatisflex.core.query.QueryCondition;
import com.xtc.marketing.invoiceservice.config.BaseDao;
import com.xtc.marketing.invoiceservice.invoice.dao.mapper.GoodsMapper;
import com.xtc.marketing.invoiceservice.invoice.dataobject.GoodsDO;
import com.xtc.marketing.invoiceservice.invoice.dto.query.GoodsPageQry;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

import static com.xtc.marketing.invoiceservice.invoice.dataobject.table.GoodsDOTableDef.GOODS_DO;

/**
 * 商品数据库访问类
 * <p>原则上不写 sql 语句</p>
 * <p>从模板项目中获取 BaseDao 也可以使用 ServiceImpl</p>
 * <p>参数校验使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.If} 包含常用的：非空、非空集合、非空字符串等</p>
 * <p>SQL 函数使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.QueryMethods}</p>
 */
@Repository
public class GoodsDao extends BaseDao<GoodsMapper, GoodsDO> {

    /**
     * 查询商品分页列表
     *
     * @param qry 参数
     * @return 商品分页列表
     */
    public Page<GoodsDO> pageBy(GoodsPageQry qry) {
        Page<GoodsDO> page = Page.of(qry.getPageIndex(), qry.getPageSize());
        return queryChain()
                .where(GOODS_DO.ERP_CODE.eq(qry.getErpCode(), If::hasText))
                .and(
                        QueryCondition.createEmpty()
                                .and("MATCH (" + GOODS_DO.ERP_NAME.getName() + ") AGAINST (? IN BOOLEAN MODE)", "+" + qry.getErpName())
                                .when(If.hasText(qry.getErpName()))
                )
                .orderBy(GOODS_DO.ID.desc())
                .page(page);
    }

    /**
     * 查询商品
     *
     * @param erpCode 物料代码
     * @return 商品
     */
    public Optional<GoodsDO> getByErpCode(String erpCode) {
        if (If.noText(erpCode)) {
            return Optional.empty();
        }
        return queryChain().where(GOODS_DO.ERP_CODE.eq(erpCode)).oneOpt();
    }

    /**
     * 判断商品存在
     *
     * @param erpCode 物料代码
     * @return 执行结果
     */
    public boolean existsByErpCode(String erpCode) {
        if (If.noText(erpCode)) {
            return false;
        }
        return queryChain().where(GOODS_DO.ERP_CODE.eq(erpCode)).exists();
    }

    /**
     * 判断商品存在，并排除当前商品id
     *
     * @param erpCode   物料代码
     * @param excludeId 商品id
     * @return 执行结果
     */
    public boolean existsByCodeExcludeId(String erpCode, Long excludeId) {
        if (If.noText(erpCode) || If.isNull(excludeId)) {
            return false;
        }
        return queryChain()
                .where(GOODS_DO.ERP_CODE.eq(erpCode))
                .and(GOODS_DO.ID.ne(excludeId))
                .exists();
    }

    /**
     * 查询导出的商品列表
     *
     * @param lastId 上次查询的最后一条记录id
     * @return 商品列表
     */
    public List<GoodsDO> listForExport(Long lastId) {
        return queryChain()
                .and(GOODS_DO.ID.lt(lastId, If::notNull))
                .orderBy(GOODS_DO.ID.desc())
                .limit(LIMIT_LIST)
                .list();
    }

    /**
     * 统计导出的商品数量
     *
     * @return 导出的商品数量
     */
    public long countForExport() {
        return queryChain().count();
    }

}
