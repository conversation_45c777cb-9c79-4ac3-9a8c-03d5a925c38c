package com.xtc.marketing.invoiceservice.support.dao;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.If;
import com.mybatisflex.core.query.QueryCondition;
import com.xtc.marketing.invoiceservice.config.BaseDao;
import com.xtc.marketing.invoiceservice.support.dao.mapper.TaskMapper;
import com.xtc.marketing.invoiceservice.support.dataobject.TaskDO;
import com.xtc.marketing.invoiceservice.support.dto.query.TaskPageQry;
import com.xtc.marketing.invoiceservice.support.enums.TaskStatusEnum;
import org.springframework.stereotype.Repository;

import java.util.Optional;

import static com.xtc.marketing.invoiceservice.support.dataobject.table.TaskDOTableDef.TASK_DO;

/**
 * 任务数据库访问类
 * <p>原则上不写 sql 语句</p>
 * <p>从模板项目中获取 BaseDao 也可以使用 ServiceImpl</p>
 * <p>参数校验使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.If} 包含常用的：非空、非空集合、非空字符串等</p>
 * <p>SQL 函数使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.QueryMethods}</p>
 */
@Repository
public class TaskDao extends BaseDao<TaskMapper, TaskDO> {

    /**
     * 查询任务分页列表
     *
     * @param qry 参数
     * @return 任务分页列表
     */
    public Page<TaskDO> pageBy(TaskPageQry qry) {
        Page<TaskDO> page = Page.of(qry.getPageIndex(), qry.getPageSize());
        return queryChain()
                .where(TASK_DO.TASK_ID.eq(qry.getTaskId(), If::hasText))
                .and(TASK_DO.LOG_ID.eq(qry.getLogId(), If::hasText))
                .and(TASK_DO.TASK_STATUS.eq(qry.getTaskStatus(), If::notNull))
                .and(
                        QueryCondition.createEmpty()
                                .and("CAST(`" + TASK_DO.CREATE_BY.getName() + "`->>'$.code' AS CHAR(50)) = ?", qry.getCreator())
                                .when(If.hasText(qry.getCreator()))
                )
                .orderBy(TASK_DO.ID.desc())
                .page(page);
    }

    /**
     * 查询任务
     *
     * @param taskId 任务id
     * @return 任务
     */
    public Optional<TaskDO> getByTaskId(String taskId) {
        if (If.noText(taskId)) {
            return Optional.empty();
        }
        return queryChain()
                .where(TASK_DO.TASK_ID.eq(taskId))
                .orderBy(TASK_DO.ID.desc())
                .limit(LIMIT_ONE)
                .oneOpt();
    }

    /**
     * 查询正在运行的任务分页列表
     *
     * @param pageIndex 页码
     * @param pageSize  数据量
     * @return 任务分页列表
     */
    public Page<TaskDO> pageRunningTask(int pageIndex, int pageSize) {
        Page<TaskDO> page = Page.of(pageIndex, pageSize);
        return queryChain()
                .where(TASK_DO.TASK_STATUS.eq(TaskStatusEnum.RUNNING))
                .orderBy(TASK_DO.UPDATE_TIME.asc())
                .page(page);
    }

    /**
     * 判断任务数量超过限制
     *
     * @param employeeId 工号
     * @param limit      限制数量
     * @return 是否超过限制
     */
    public boolean isTaskCountExceedLimit(String employeeId, int limit) {
        if (If.noText(employeeId) || limit < 1) {
            return false;
        }
        long count = queryChain()
                .where(TASK_DO.TASK_STATUS.in(TaskStatusEnum.PENDING, TaskStatusEnum.RUNNING))
                .and(
                        QueryCondition.createEmpty()
                                .and("CAST(`" + TASK_DO.CREATE_BY.getName() + "`->>'$.code' AS CHAR(50)) = ?", employeeId)
                )
                .count();
        return count >= limit;
    }

    /**
     * 更新任务状态
     *
     * @param id         任务id
     * @param taskStatus 任务状态
     */
    public void updateTaskStatus(long id, TaskStatusEnum taskStatus) {
        if (id < 1 || taskStatus == null) {
            return;
        }
        updateChain()
                .set(TASK_DO.TASK_STATUS, taskStatus)
                .where(TASK_DO.ID.eq(id))
                .limit(LIMIT_ONE)
                .update();
    }

    /**
     * 更新任务文件
     *
     * @param id     任务id
     * @param fileId 文件id
     */
    public void updateTaskFile(long id, String fileId) {
        if (id < 1 || If.noText(fileId)) {
            return;
        }
        updateChain()
                .set(TASK_DO.FILE_ID, fileId)
                .where(TASK_DO.ID.eq(id))
                .limit(LIMIT_ONE)
                .update();
    }

}
