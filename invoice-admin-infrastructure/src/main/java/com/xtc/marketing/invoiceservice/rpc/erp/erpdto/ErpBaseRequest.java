package com.xtc.marketing.invoiceservice.rpc.erp.erpdto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.http.HttpMethod;

/**
 * ERP系统请求基类
 */
@Getter
@Setter
@ToString
public abstract class ErpBaseRequest<T> {

    /**
     * 获取 API 路径
     *
     * @return API 路径
     */
    public abstract String getApiPath();

    /**
     * 获取请求方法
     *
     * @return 请求方法
     */
    public abstract HttpMethod getRequestMethod();

    /**
     * 获取响应类
     *
     * @return 响应类
     */
    public abstract Class<T> getResponseClass();

}
