package com.xtc.marketing.invoiceservice.invoice.dao;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.If;
import com.mybatisflex.core.query.QueryCondition;
import com.xtc.marketing.invoiceservice.config.BaseDao;
import com.xtc.marketing.invoiceservice.invoice.dao.mapper.BuyerMapper;
import com.xtc.marketing.invoiceservice.invoice.dataobject.BuyerDO;
import com.xtc.marketing.invoiceservice.invoice.dto.query.BuyerPageQry;
import org.springframework.stereotype.Repository;

import java.util.List;

import static com.xtc.marketing.invoiceservice.invoice.dataobject.table.BuyerDOTableDef.BUYER_DO;

/**
 * 购买方数据库访问类
 * <p>原则上不写 sql 语句</p>
 * <p>从模板项目中获取 BaseDao 也可以使用 ServiceImpl</p>
 * <p>参数校验使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.If} 包含常用的：非空、非空集合、非空字符串等</p>
 * <p>SQL 函数使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.QueryMethods}</p>
 */
@Repository
public class BuyerDao extends BaseDao<BuyerMapper, BuyerDO> {

    /**
     * 查询购买方分页列表
     *
     * @param qry 参数
     * @return 购买方分页列表
     */
    public Page<BuyerDO> pageBy(BuyerPageQry qry) {
        Page<BuyerDO> page = Page.of(qry.getPageIndex(), qry.getPageSize());
        return queryChain()
                .where(BUYER_DO.BUYER_CODE.eq(qry.getBuyerCode(), If::hasText))
                .and(BUYER_DO.BUYER_IDENTIFY_NO.eq(qry.getBuyerIdentifyNo(), If::hasText))
                .and(
                        QueryCondition.createEmpty()
                                .and("MATCH (" + BUYER_DO.BUYER_NAME.getName() + ") AGAINST (? IN BOOLEAN MODE)", "+" + qry.getBuyerName())
                                .when(If.hasText(qry.getBuyerName()))
                )
                .and(BUYER_DO.ENABLED.eq(qry.getEnabled(), If::notNull))
                .orderBy(BUYER_DO.ID.desc())
                .page(page);
    }

    /**
     * 判断购买方存在
     *
     * @param buyerCode 购买方代码
     * @return 执行结果
     */
    public boolean existsByBuyerCode(String buyerCode) {
        if (If.noText(buyerCode)) {
            return false;
        }
        return queryChain().where(BUYER_DO.BUYER_CODE.eq(buyerCode)).exists();
    }

    /**
     * 判断购买方存在，并排除当前购买方id
     *
     * @param buyerCode 购买方代码
     * @param excludeId 购买方id
     * @return 执行结果
     */
    public boolean existsByCodeExcludeId(String buyerCode, Long excludeId) {
        if (If.noText(buyerCode) || If.isNull(excludeId)) {
            return false;
        }
        return queryChain()
                .where(BUYER_DO.BUYER_CODE.eq(buyerCode))
                .and(BUYER_DO.ID.ne(excludeId))
                .exists();
    }

    /**
     * 查询导出的购买方列表
     *
     * @param lastId 上次查询的最后一条记录id
     * @return 购买方列表
     */
    public List<BuyerDO> listForExport(Long lastId) {
        return queryChain()
                .and(BUYER_DO.ID.lt(lastId, If::notNull))
                .orderBy(BUYER_DO.ID.desc())
                .limit(LIMIT_LIST)
                .list();
    }

    /**
     * 统计导出的购买方数量
     *
     * @return 导出的购买方数量
     */
    public long countForExport() {
        return queryChain().count();
    }

}
