package com.xtc.marketing.invoiceservice.invoice.dao;

import com.xtc.marketing.invoiceservice.config.BaseDao;
import com.xtc.marketing.invoiceservice.invoice.dao.mapper.InvoiceItemMapper;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceItemDO;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceItemListQry;
import org.springframework.stereotype.Repository;

import java.util.List;

import static com.xtc.marketing.invoiceservice.invoice.dataobject.table.InvoiceItemDOTableDef.INVOICE_ITEM_DO;

/**
 * 发票项目数据库访问类
 * <p>原则上不写 sql 语句</p>
 * <p>从模板项目中获取 BaseDao 也可以使用 ServiceImpl</p>
 * <p>参数校验使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.If} 包含常用的：非空、非空集合、非空字符串等</p>
 * <p>SQL 函数使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.QueryMethods}</p>
 */
@Repository
public class InvoiceItemDao extends BaseDao<InvoiceItemMapper, InvoiceItemDO> {

    /**
     * 查询发票项目列表
     *
     * @param qry 参数
     * @return 发票项目列表
     */
    public List<InvoiceItemDO> listBy(InvoiceItemListQry qry) {
        if (noCondition(qry)) {
            return List.of();
        }
        return queryChain()
                .where(INVOICE_ITEM_DO.INVOICE_ID.eq(qry.getInvoiceId()))
                .orderBy(INVOICE_ITEM_DO.ITEM_NO.asc(), INVOICE_ITEM_DO.ID.asc())
                .limit(LIMIT_LIST)
                .list();
    }

}
