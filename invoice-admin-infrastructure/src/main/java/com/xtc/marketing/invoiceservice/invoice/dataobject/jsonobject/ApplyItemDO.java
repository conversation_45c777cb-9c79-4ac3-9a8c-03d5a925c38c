package com.xtc.marketing.invoiceservice.invoice.dataobject.jsonobject;

import com.xtc.marketing.invoiceservice.invoice.enums.InvoiceItemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发票申请项目
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplyItemDO {

    /**
     * 物料代码
     */
    private String erpCode;
    /**
     * 发票项目类型
     */
    private InvoiceItemTypeEnum itemType;
    /**
     * 数量
     */
    private Integer num;
    /**
     * 总价（单位：分）
     */
    private Integer totalPrice;

}
