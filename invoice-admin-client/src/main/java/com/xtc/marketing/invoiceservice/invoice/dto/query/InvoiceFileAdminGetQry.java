package com.xtc.marketing.invoiceservice.invoice.dto.query;

import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

/**
 * 发票文件查询参数
 */
@Getter
@Setter
@ToString
public class InvoiceFileAdminGetQry {

    /**
     * 发票id
     */
    @NotBlank
    @Length(max = 50)
    private String invoiceId;

}
