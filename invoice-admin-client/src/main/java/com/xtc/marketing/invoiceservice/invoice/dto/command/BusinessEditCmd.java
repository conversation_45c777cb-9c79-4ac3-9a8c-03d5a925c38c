package com.xtc.marketing.invoiceservice.invoice.dto.command;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

/**
 * 业务接入编辑参数
 */
@Getter
@Setter
@ToString
public class BusinessEditCmd {

    /**
     * 业务代码
     */
    @Length(max = 50)
    private String bizCode;
    /**
     * 业务名称
     */
    @Length(max = 50)
    private String bizName;
    /**
     * 启用标识
     */
    private Boolean enabled;

}
