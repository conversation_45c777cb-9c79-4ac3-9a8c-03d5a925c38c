package com.xtc.marketing.invoiceservice.invoice.dto;

import com.xtc.marketing.invoiceservice.invoice.enums.InvoiceItemTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 发票申请项目
 */
@Getter
@Setter
@ToString
public class ApplyItemDTO {

    /**
     * 物料代码
     */
    private String erpCode;
    /**
     * 发票项目类型
     */
    private InvoiceItemTypeEnum itemType;
    /**
     * 数量
     */
    private Integer num;
    /**
     * 总价（单位：分）
     */
    private Integer totalPrice;

}
