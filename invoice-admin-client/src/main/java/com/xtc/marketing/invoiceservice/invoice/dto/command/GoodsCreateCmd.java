package com.xtc.marketing.invoiceservice.invoice.dto.command;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xtc.marketing.invoiceservice.support.dto.BaseDTO;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;

/**
 * 商品新增参数
 */
@Getter
@Setter
@ToString
public class GoodsCreateCmd {

    /**
     * 物料代码
     */
    @NotBlank
    @Length(max = 50)
    private String erpCode;
    /**
     * 物料名称
     */
    @NotBlank
    @Length(max = 200)
    private String erpName;
    /**
     * 商品名称
     */
    @NotBlank
    @Length(max = 50)
    private String goodsName;
    /**
     * 税收分类编码
     */
    @NotBlank
    @Length(max = 50)
    private String taxClassificationCode;
    /**
     * 税务名称
     */
    @NotBlank
    @Length(max = 50)
    private String taxItemName;
    /**
     * 税率（13%=0.13）
     */
    @NotNull
    @Digits(integer = 3, fraction = 2)
    private BigDecimal taxRate;
    /**
     * 规格型号
     */
    @NotBlank
    @Length(max = 200)
    private String specification;
    /**
     * 单位
     */
    @NotBlank
    @Length(max = 10)
    private String unit;
    /**
     * 更新人
     */
    @JsonIgnore
    private BaseDTO.OperatorDTO updateBy;
    /**
     * 创建人
     */
    @JsonIgnore
    private BaseDTO.OperatorDTO createBy;

}
