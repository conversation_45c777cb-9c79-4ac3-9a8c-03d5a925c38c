package com.xtc.marketing.invoiceservice.invoice.dto.query;

import com.xtc.marketing.dto.BasePageQuery;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDate;

/**
 * 发票申请分页查询参数
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@SuperBuilder
public class InvoiceApplyPageQry extends BasePageQuery {

    /**
     * 业务代码
     */
    @Length(max = 50)
    private String bizCode;
    /**
     * 申请id
     */
    @Length(max = 50)
    private String applyId;
    /**
     * 发票id
     */
    @Length(max = 50)
    private String invoiceId;
    /**
     * 业务订单编号
     */
    @Length(max = 50)
    private String bizOrderId;
    /**
     * 开票流水号
     */
    @Length(max = 50)
    private String serialNo;
    /**
     * 购买方税号
     */
    @Length(max = 50)
    private String buyerIdentifyNo;
    /**
     * 创建日期开始
     */
    private LocalDate createDateStart;
    /**
     * 创建日期结束
     */
    private LocalDate createDateEnd;

}
