package com.xtc.marketing.invoiceservice.invoice.dto.command;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xtc.marketing.invoiceservice.support.dto.BaseDTO;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

/**
 * 购买方新增参数
 */
@Getter
@Setter
@ToString
public class BuyerCreateCmd {

    /**
     * 购买方代码
     */
    @NotBlank
    @Length(max = 50)
    private String buyerCode;
    /**
     * 购买方税号
     */
    @NotBlank
    @Length(max = 50)
    private String buyerIdentifyNo;
    /**
     * 购买方名称
     */
    @NotBlank
    @Length(max = 50)
    private String buyerName;
    /**
     * 购买方电话
     */
    @Length(max = 50)
    private String buyerPhone;
    /**
     * 购买方银行
     */
    @Length(max = 50)
    private String buyerBankName;
    /**
     * 购买方银行账号
     */
    @Length(max = 50)
    private String buyerBankAccount;
    /**
     * 购买方地址
     */
    @Length(max = 200)
    private String buyerAddress;
    /**
     * 启用标识
     */
    @NotNull
    private Boolean enabled;
    /**
     * 更新人
     */
    @JsonIgnore
    private BaseDTO.OperatorDTO updateBy;
    /**
     * 创建人
     */
    @JsonIgnore
    private BaseDTO.OperatorDTO createBy;

}
