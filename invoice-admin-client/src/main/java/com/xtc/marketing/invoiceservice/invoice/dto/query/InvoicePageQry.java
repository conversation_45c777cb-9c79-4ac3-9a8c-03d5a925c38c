package com.xtc.marketing.invoiceservice.invoice.dto.query;

import com.xtc.marketing.dto.BasePageQuery;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDate;

/**
 * 发票分页查询参数
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@SuperBuilder
public class InvoicePageQry extends BasePageQuery {

    /**
     * 业务代码
     */
    @Length(max = 50)
    private String bizCode;
    /**
     * 发票id
     */
    @Length(max = 50)
    private String invoiceId;
    /**
     * 开票流水号
     */
    @Length(max = 50)
    private String serialNo;
    /**
     * 蓝票号码
     */
    @Length(max = 50)
    private String blueInvoiceNo;
    /**
     * 红票号码
     */
    @Length(max = 50)
    private String redInvoiceNo;
    /**
     * 业务订单编号
     */
    @Length(max = 50)
    private String bizOrderId;
    /**
     * 购买方税号
     */
    @Length(max = 50)
    private String buyerIdentifyNo;
    /**
     * 开票日期开始
     */
    private LocalDate invoiceDateStart;
    /**
     * 开票日期结束
     */
    private LocalDate invoiceDateEnd;

}
