package com.xtc.marketing.invoiceservice.invoice.dto.query;

import com.xtc.marketing.dto.BasePageQuery;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;

/**
 * 业务接入分页查询参数
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@SuperBuilder
public class BusinessPageQry extends BasePageQuery {

    /**
     * 业务代码
     */
    @Length(max = 50)
    private String bizCode;
    /**
     * 业务名称
     */
    @Length(max = 50)
    private String bizName;
    /**
     * 启用标识
     */
    private Boolean enabled;

}
