package com.xtc.marketing.invoiceservice.invoice.dto.command;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

/**
 * 销售方编辑参数
 */
@Getter
@Setter
@ToString
public class SellerEditCmd {

    /**
     * 销售方代码
     */
    @Length(max = 50)
    private String sellerCode;
    /**
     * 销售方税号
     */
    @Length(max = 50)
    private String sellerIdentifyNo;
    /**
     * 销售方名称
     */
    @Length(max = 50)
    private String sellerName;
    /**
     * 销售方电话
     */
    @Length(max = 50)
    private String sellerPhone;
    /**
     * 销售方银行
     */
    @Length(max = 50)
    private String sellerBankName;
    /**
     * 销售方银行账号
     */
    @Length(max = 50)
    private String sellerBankAccount;
    /**
     * 销售方地址
     */
    @Length(max = 200)
    private String sellerAddress;
    /**
     * 开票人
     */
    @Length(max = 20)
    private String operator;
    /**
     * 启用标识
     */
    private Boolean enabled;

}
