package com.xtc.marketing.invoiceservice.invoice.dto.query;

import com.xtc.marketing.dto.BasePageQuery;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;

/**
 * 商品分页查询参数
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@SuperBuilder
public class GoodsPageQry extends BasePageQuery {

    /**
     * 物料代码
     */
    @Length(max = 50)
    private String erpCode;
    /**
     * 物料名称
     */
    @Length(max = 50)
    private String erpName;

}
