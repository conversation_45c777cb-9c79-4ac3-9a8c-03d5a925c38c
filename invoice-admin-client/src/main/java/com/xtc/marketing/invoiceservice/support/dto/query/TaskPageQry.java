package com.xtc.marketing.invoiceservice.support.dto.query;

import com.xtc.marketing.dto.BasePageQuery;
import com.xtc.marketing.invoiceservice.support.enums.TaskStatusEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

/**
 * 任务分页列表参数
 */
@Getter
@Setter
@ToString
public class TaskPageQry extends BasePageQuery {

    /**
     * 任务id
     */
    @Length(max = 50)
    private String taskId;
    /**
     * 日志id
     */
    @Length(max = 50)
    private String logId;
    /**
     * 任务状态
     */
    private TaskStatusEnum taskStatus;
    /**
     * 创建人
     */
    @Length(max = 50)
    private String creator;

}
