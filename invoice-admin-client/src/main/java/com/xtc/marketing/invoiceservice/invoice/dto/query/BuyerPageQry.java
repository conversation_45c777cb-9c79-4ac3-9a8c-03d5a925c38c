package com.xtc.marketing.invoiceservice.invoice.dto.query;

import com.xtc.marketing.dto.BasePageQuery;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;

/**
 * 购买方分页查询参数
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@SuperBuilder
public class BuyerPageQry extends BasePageQuery {

    /**
     * 购买方代码
     */
    @Length(max = 50)
    private String buyerCode;
    /**
     * 购买方税号
     */
    @Length(max = 50)
    private String buyerIdentifyNo;
    /**
     * 购买方名称
     */
    @Length(max = 50)
    private String buyerName;
    /**
     * 启用标识
     */
    private Boolean enabled;

}
