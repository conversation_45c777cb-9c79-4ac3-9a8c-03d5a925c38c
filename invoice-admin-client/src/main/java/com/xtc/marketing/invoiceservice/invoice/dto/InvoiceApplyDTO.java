package com.xtc.marketing.invoiceservice.invoice.dto;

import com.xtc.marketing.invoiceservice.invoice.enums.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 发票申请
 */
@Getter
@Setter
@ToString
public class InvoiceApplyDTO {

    /**
     * 申请id
     */
    private String applyId;
    /**
     * 申请状态
     */
    private ApplyStateEnum applyState;
    /**
     * 申请记录
     */
    private List<ApplyHistoryDTO> applyHistory;
    /**
     * 发票id
     */
    private String invoiceId;
    /**
     * 业务代码
     */
    private String bizCode;
    /**
     * 业务订单编号
     */
    private String bizOrderId;
    /**
     * 业务订单标签
     */
    private List<TagDTO> bizOrderTag;
    /**
     * 平台代码
     */
    private PlatformCodeEnum platformCode;
    /**
     * 开票流水号
     */
    private String serialNo;
    /**
     * 开票类型
     */
    private CreateTypeEnum createType;
    /**
     * 发票类型
     */
    private InvoiceTypeEnum invoiceType;
    /**
     * 发票业务类型
     */
    private InvoiceBizTypeEnum invoiceBizType;
    /**
     * 发票抬头
     */
    private String invoiceTitle;
    /**
     * 购买方税号
     */
    private String buyerIdentifyNo;
    /**
     * 购买方电话
     */
    private String buyerPhone;
    /**
     * 购买方地址
     */
    private String buyerAddress;
    /**
     * 购买方银行
     */
    private String buyerBankName;
    /**
     * 购买方银行账号
     */
    private String buyerBankAccount;
    /**
     * 销售方代码
     */
    private String sellerCode;
    /**
     * 蓝票的发票id
     */
    private String blueInvoiceId;
    /**
     * 冲红原因
     */
    private String redReason;
    /**
     * 发票备注
     */
    private String invoiceRemark;
    /**
     * 发票申请项目
     */
    private List<ApplyItemDTO> applyItems;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
