package com.xtc.marketing.invoiceservice.invoice.dto.command;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

/**
 * 购买方编辑参数
 */
@Getter
@Setter
@ToString
public class BuyerEditCmd {

    /**
     * 购买方代码
     */
    @Length(max = 50)
    private String buyerCode;
    /**
     * 购买方税号
     */
    @Length(max = 50)
    private String buyerIdentifyNo;
    /**
     * 购买方名称
     */
    @Length(max = 50)
    private String buyerName;
    /**
     * 购买方电话
     */
    @Length(max = 50)
    private String buyerPhone;
    /**
     * 购买方银行
     */
    @Length(max = 50)
    private String buyerBankName;
    /**
     * 购买方银行账号
     */
    @Length(max = 50)
    private String buyerBankAccount;
    /**
     * 购买方地址
     */
    @Length(max = 200)
    private String buyerAddress;
    /**
     * 启用标识
     */
    private Boolean enabled;

}
