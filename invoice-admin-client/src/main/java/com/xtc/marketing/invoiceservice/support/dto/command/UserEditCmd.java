package com.xtc.marketing.invoiceservice.support.dto.command;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

/**
 * 用户编辑参数
 */
@Getter
@Setter
@ToString
public class UserEditCmd {

    /**
     * 用户名称
     */
    @Length(max = 50)
    private String userName;
    /**
     * 工号
     */
    @Length(max = 50)
    private String employeeId;
    /**
     * 业务代码
     */
    @Length(max = 50)
    private String bizCode;
    /**
     * 启用标识
     */
    private Boolean enabled;

}
