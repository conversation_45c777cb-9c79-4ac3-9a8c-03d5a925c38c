package com.xtc.marketing.invoiceservice.invoice.dto.command;

import jakarta.validation.constraints.Digits;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;

/**
 * 商品编辑参数
 */
@Getter
@Setter
@ToString
public class GoodsEditCmd {

    /**
     * 物料代码
     */
    @Length(max = 50)
    private String erpCode;
    /**
     * 物料名称
     */
    @Length(max = 200)
    private String erpName;
    /**
     * 商品名称
     */
    @Length(max = 50)
    private String goodsName;
    /**
     * 税收分类编码
     */
    @Length(max = 50)
    private String taxClassificationCode;
    /**
     * 税务名称
     */
    @Length(max = 50)
    private String taxItemName;
    /**
     * 税率（13%=0.13）
     * `tax_rate` decimal(3,2) DEFAULT '0.00' COMMENT '税率（13%=0.13）',
     */
    @Digits(integer = 3, fraction = 2)
    private BigDecimal taxRate;
    /**
     * 规格型号
     */
    @Length(max = 200)
    private String specification;
    /**
     * 单位
     */
    @Length(max = 10)
    private String unit;

}
