package com.xtc.marketing.invoiceservice.invoice.dto.query;

import com.xtc.marketing.dto.BasePageQuery;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;

/**
 * 销售方分页查询参数
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@SuperBuilder
public class SellerPageQry extends BasePageQuery {

    /**
     * 销售方代码
     */
    @Length(max = 50)
    private String sellerCode;
    /**
     * 销售方税号
     */
    @Length(max = 50)
    private String sellerIdentifyNo;
    /**
     * 销售方名称
     */
    @Length(max = 50)
    private String sellerName;
    /**
     * 启用标识
     */
    private Boolean enabled;

}
