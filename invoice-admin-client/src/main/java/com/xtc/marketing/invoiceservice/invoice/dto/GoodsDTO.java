package com.xtc.marketing.invoiceservice.invoice.dto;

import com.xtc.marketing.invoiceservice.support.dto.BaseDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 商品
 */
@Getter
@Setter
@ToString
public class GoodsDTO extends BaseDTO {

    /**
     * 物料代码
     */
    private String erpCode;
    /**
     * 物料名称
     */
    private String erpName;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 税收分类编码
     */
    private String taxClassificationCode;
    /**
     * 税务名称
     */
    private String taxItemName;
    /**
     * 税率（13%=0.13）
     */
    private BigDecimal taxRate;
    /**
     * 规格型号
     */
    private String specification;
    /**
     * 单位
     */
    private String unit;

}
