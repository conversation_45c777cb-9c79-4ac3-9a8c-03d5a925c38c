package com.xtc.marketing.invoiceservice.invoice.dto.command;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

/**
 * 业务接入新增参数
 */
@Getter
@Setter
@ToString
public class BusinessCreateCmd {

    /**
     * 业务代码
     */
    @NotBlank
    @Length(max = 50)
    private String bizCode;
    /**
     * 业务名称
     */
    @NotBlank
    @Length(max = 50)
    private String bizName;
    /**
     * 启用标识
     */
    @NotNull
    private Boolean enabled;

}
