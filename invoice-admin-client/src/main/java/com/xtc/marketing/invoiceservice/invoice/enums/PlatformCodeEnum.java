package com.xtc.marketing.invoiceservice.invoice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;

/**
 * 平台代码枚举
 */
@Getter
@AllArgsConstructor
public enum PlatformCodeEnum {
    /**
     * 官方商城
     */
    XTC_SHOP("官方商城"),
    /**
     * 会员商城
     */
    XTC_MALL("会员商城"),
    /**
     * 内部购机
     */
    XTC_INTERNAL_SHOP("内部购机"),
    /**
     * 天猫
     */
    TMALL("天猫"),
    /**
     * 京东
     */
    JD("京东"),
    /**
     * 抖音
     */
    TIKTOK("抖音"),
    /**
     * 拼多多
     */
    PDD("拼多多"),
    /**
     * 快手
     */
    KUAISHOU("快手"),
    /**
     * 小红书
     */
    XIAOHONGSHU("小红书"),
    /**
     * 微信视频号小店
     */
    WECHAT_CHANNELS_SHOP("微信视频号小店"),
    /**
     * 其他（可以把平台填到发票备注里面）
     */
    OTHER("其他"),
    ;

    private final String desc;

    /**
     * 获取枚举
     *
     * @param value 枚举值
     * @return 枚举
     */
    public static Optional<PlatformCodeEnum> of(String value) {
        if (value == null) {
            return Optional.empty();
        }
        try {
            return Optional.of(PlatformCodeEnum.valueOf(value.toUpperCase()));
        } catch (IllegalArgumentException e) {
            return Optional.empty();
        }
    }

    /**
     * 判断相等
     *
     * @param name 枚举值
     * @return 执行结果
     */
    public boolean equalsName(String name) {
        return this.name().equalsIgnoreCase(name);
    }
}
