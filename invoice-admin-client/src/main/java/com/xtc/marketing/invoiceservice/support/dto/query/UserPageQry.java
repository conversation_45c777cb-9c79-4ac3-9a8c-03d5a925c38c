package com.xtc.marketing.invoiceservice.support.dto.query;

import com.xtc.marketing.dto.BasePageQuery;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;

/**
 * 用户分页查询参数
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@SuperBuilder
public class UserPageQry extends BasePageQuery {

    /**
     * 用户名称
     */
    @Length(max = 50)
    private String userName;
    /**
     * 工号
     */
    @Length(max = 50)
    private String employeeId;
    /**
     * 业务代码
     */
    @Length(max = 50)
    private String bizCode;
    /**
     * 启用标识
     */
    private Boolean enabled;

}
