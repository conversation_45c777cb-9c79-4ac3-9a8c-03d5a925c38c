package com.xtc.marketing.invoiceservice.invoice.dto;

import com.xtc.marketing.invoiceservice.invoice.enums.CreateTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.enums.InvoiceBizTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.enums.InvoiceTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.enums.PlatformCodeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 发票
 */
@Getter
@Setter
@ToString
public class InvoiceDTO {

    /**
     * 发票id
     */
    private String invoiceId;
    /**
     * 发票文件链接
     */
    private String fileUrl;
    /**
     * 发票文件凭证
     */
    private String fileToken;
    /**
     * 业务代码
     */
    private String bizCode;
    /**
     * 业务订单编号
     */
    private String bizOrderId;
    /**
     * 业务订单标签
     */
    private List<TagDTO> bizOrderTag;
    /**
     * 平台代码
     */
    private PlatformCodeEnum platformCode;
    /**
     * 开票流水号
     */
    private String serialNo;
    /**
     * 蓝票号码
     */
    private String blueInvoiceNo;
    /**
     * 红票号码
     */
    private String redInvoiceNo;
    /**
     * 开票类型
     */
    private CreateTypeEnum createType;
    /**
     * 发票类型
     */
    private InvoiceTypeEnum invoiceType;
    /**
     * 发票业务类型
     */
    private InvoiceBizTypeEnum invoiceBizType;
    /**
     * 开票时间
     */
    private LocalDateTime invoiceTime;
    /**
     * 开票金额
     */
    private Integer invoiceAmount;
    /**
     * 合计税额
     */
    private Integer taxAmount;
    /**
     * 合计不含税总金额
     */
    private Integer priceAmount;
    /**
     * 发票抬头
     */
    private String invoiceTitle;
    /**
     * 购买方税号
     */
    private String buyerIdentifyNo;
    /**
     * 购买方电话
     */
    private String buyerPhone;
    /**
     * 购买方地址
     */
    private String buyerAddress;
    /**
     * 购买方银行
     */
    private String buyerBankName;
    /**
     * 购买方银行账号
     */
    private String buyerBankAccount;
    /**
     * 开票人
     */
    private String operator;
    /**
     * 销售方税号
     */
    private String sellerIdentifyNo;
    /**
     * 销售方名称
     */
    private String sellerName;
    /**
     * 销售方电话
     */
    private String sellerPhone;
    /**
     * 销售方地址
     */
    private String sellerAddress;
    /**
     * 销售方银行
     */
    private String sellerBankName;
    /**
     * 销售方银行账号
     */
    private String sellerBankAccount;
    /**
     * 冲红原因
     */
    private String redReason;
    /**
     * 发票备注
     */
    private String invoiceRemark;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
