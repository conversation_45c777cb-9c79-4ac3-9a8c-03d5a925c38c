package com.xtc.marketing.invoiceservice.invoice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;

/**
 * 开票类型枚举
 */
@Getter
@AllArgsConstructor
public enum CreateTypeEnum {
    /**
     * 蓝票
     */
    BLUE("蓝票"),
    /**
     * 红票
     */
    RED("红票"),
    ;

    private final String desc;

    /**
     * 获取枚举
     *
     * @param value 枚举值
     * @return 枚举
     */
    public static Optional<CreateTypeEnum> of(String value) {
        if (value == null) {
            return Optional.empty();
        }
        try {
            return Optional.of(CreateTypeEnum.valueOf(value.toUpperCase()));
        } catch (IllegalArgumentException e) {
            return Optional.empty();
        }
    }

    /**
     * 判断相等
     *
     * @param name 枚举值
     * @return 执行结果
     */
    public boolean equalsName(String name) {
        return this.name().equalsIgnoreCase(name);
    }
}
