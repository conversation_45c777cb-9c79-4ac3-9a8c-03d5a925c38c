package com.xtc.marketing.invoiceservice.invoice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;

/**
 * 发票抬头类型枚举
 */
@Getter
@AllArgsConstructor
public enum InvoiceBizTypeEnum {
    /**
     * 个人
     */
    PERSONAL("个人"),
    /**
     * 企业
     */
    COMPANY("企业"),
    ;

    private final String desc;

    /**
     * 获取枚举
     *
     * @param value 枚举值
     * @return 枚举
     */
    public static Optional<InvoiceBizTypeEnum> of(String value) {
        if (value == null) {
            return Optional.empty();
        }
        try {
            return Optional.of(InvoiceBizTypeEnum.valueOf(value.toUpperCase()));
        } catch (IllegalArgumentException e) {
            return Optional.empty();
        }
    }

    /**
     * 判断相等
     *
     * @param name 枚举值
     * @return 执行结果
     */
    public boolean equalsName(String name) {
        return this.name().equalsIgnoreCase(name);
    }
}
