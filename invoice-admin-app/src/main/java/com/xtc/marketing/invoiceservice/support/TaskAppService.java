package com.xtc.marketing.invoiceservice.support;

import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.invoiceservice.support.dto.TaskDTO;
import com.xtc.marketing.invoiceservice.support.dto.query.TaskPageQry;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;

/**
 * 任务应用服务
 */
public interface TaskAppService {

    /**
     * 任务分页列表
     *
     * @param qry 参数
     * @return 任务分页列表
     */
    PageResponse<TaskDTO> pageTasks(TaskPageQry qry);

    /**
     * 任务超时处理
     *
     * @param pageIndex 页码
     * @param pageSize  数据量
     */
    void taskTimeout(int pageIndex, int pageSize);

    /**
     * 生成任务文件链接
     *
     * @param taskId 任务id
     * @return 任务文件链接
     */
    String createFileUrl(String taskId);

    /**
     * 下载任务文件
     *
     * @param fileToken 文件凭证
     * @return 任务文件
     */
    ResponseEntity<Resource> downloadTaskFile(String fileToken);

}
