package com.xtc.marketing.invoiceservice.support.context;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.RandomUtils;

import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;

/**
 * 任务上下文
 *
 * @param <R> 请求参数类型
 */
@Getter
@Setter
@ToString
@Builder
public class TaskContext<R> {

    /**
     * 任务唯一标识
     */
    private Long id;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 登录用户id
     */
    private String userId;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 总数据量
     */
    private Long total;
    /**
     * 总页数
     */
    private Integer pages;
    /**
     * 数据量
     */
    private Integer pageSize;
    /**
     * 页码
     */
    private Integer pageIndex;
    /**
     * 请求参数
     */
    private R request;
    /**
     * 临时文件，用于任务完成后清理
     */
    private Path tempFile;
    /**
     * 上次更新时的处理数量
     */
    private Long lastProcessedCount;
    /**
     * 更新阈值的随机百分比
     */
    private Integer randomPercent;

    /**
     * 清理临时文件
     */
    public void cleanupTempFile() {
        if (tempFile == null) {
            return;
        }
        try {
            Files.deleteIfExists(tempFile);
        } catch (Exception ignored) {
            // 忽略异常，临时文件删除失败不影响任务执行
        }
        tempFile = null;
    }

    /**
     * 生成新的随机百分比 (1-5%)
     */
    public void generateNewRandomPercent() {
        this.randomPercent = RandomUtils.insecure().randomInt(1, 6);
    }

}
