package com.xtc.marketing.invoiceservice.invoice;

import com.mybatisflex.core.paginate.Page;
import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.invoiceservice.exception.BizErrorCode;
import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.invoice.converter.BusinessConverter;
import com.xtc.marketing.invoiceservice.invoice.dao.BusinessDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.BusinessDO;
import com.xtc.marketing.invoiceservice.invoice.dto.BusinessDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.BusinessCreateCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.BusinessEditCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.BusinessPageQry;
import com.xtc.marketing.invoiceservice.util.BeanCopier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务接入应用服务实现类
 * <p>简单逻辑 10 行以内的代码可以直接在 service 方法内实现</p>
 * <p>复杂逻辑（或可复用逻辑）封装在 executor 里实现，调用链路：service > executor</p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BusinessAppServiceImpl implements BusinessAppService {

    // 基础设施注入
    private final BusinessDao businessDao;
    private final BusinessConverter businessConverter;

    @Override
    public PageResponse<BusinessDTO> pageBusinesses(BusinessPageQry qry) {
        Page<BusinessDO> page = businessDao.pageBy(qry);
        List<BusinessDTO> records = businessConverter.doToDto(page.getRecords());
        return PageResponse.of(records, page.getTotalRow(), page.getPageSize(), page.getPageNumber());
    }

    @Override
    public BusinessDTO businessDetail(long id) {
        BusinessDO businessDO = businessDao.getByIdOpt(id)
                .orElseThrow(() -> BizException.of(BizErrorCode.B_BUSINESS_BusinessNotExists));
        return businessConverter.doToDto(businessDO);
    }

    @Override
    public Long createBusiness(BusinessCreateCmd cmd) {
        // 参数校验
        boolean existsByBizCode = businessDao.existsByBizCode(cmd.getBizCode());
        if (existsByBizCode) {
            throw BizException.of("业务代码已存在");
        }
        // 新增业务接入
        BusinessDO businessDO = BeanCopier.copy(cmd, BusinessDO::new);
        businessDao.save(businessDO);
        return businessDO.getId();
    }

    @Override
    public void editBusiness(long id, BusinessEditCmd cmd) {
        // 参数校验
        boolean notExists = businessDao.getByIdOpt(id).isEmpty();
        if (notExists) {
            throw BizException.of(BizErrorCode.B_BUSINESS_BusinessNotExists);
        }
        if (StringUtils.isNotBlank(cmd.getBizCode())) {
            boolean existsByBizCode = businessDao.existsByCodeExcludeId(cmd.getBizCode(), id);
            if (existsByBizCode) {
                throw BizException.of("业务代码已存在");
            }
        }
        // 编辑业务接入
        BusinessDO businessDO = BeanCopier.copy(cmd, BusinessDO::new);
        businessDao.updateById(id, businessDO);
    }

    @Override
    public void removeBusiness(long id) {
        // 参数校验
        boolean notExists = businessDao.getByIdOpt(id).isEmpty();
        if (notExists) {
            throw BizException.of(BizErrorCode.B_BUSINESS_BusinessNotExists);
        }
        // 删除业务接入
        businessDao.removeById(id);
    }

}
