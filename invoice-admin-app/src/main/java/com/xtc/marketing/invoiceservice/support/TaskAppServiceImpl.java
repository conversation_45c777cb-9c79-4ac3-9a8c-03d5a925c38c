package com.xtc.marketing.invoiceservice.support;

import com.mybatisflex.core.paginate.Page;
import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.invoiceservice.oss.BizFileOssClient;
import com.xtc.marketing.invoiceservice.support.converter.TaskConverter;
import com.xtc.marketing.invoiceservice.support.dao.FileDao;
import com.xtc.marketing.invoiceservice.support.dao.TaskDao;
import com.xtc.marketing.invoiceservice.support.dataobject.FileDO;
import com.xtc.marketing.invoiceservice.support.dataobject.TaskDO;
import com.xtc.marketing.invoiceservice.support.domainservice.FileService;
import com.xtc.marketing.invoiceservice.support.domainservice.TaskService;
import com.xtc.marketing.invoiceservice.support.dto.TaskDTO;
import com.xtc.marketing.invoiceservice.support.dto.query.TaskPageQry;
import com.xtc.marketing.invoiceservice.support.enums.TaskStatusEnum;
import com.xtc.marketing.invoiceservice.util.FileDownloader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 任务应用服务实现
 * <p>简单逻辑 10 行以内的代码可以直接在 service 方法内实现</p>
 * <p>复杂逻辑（或可复用逻辑）封装在 executor 里实现，调用链路：service > executor</p>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class TaskAppServiceImpl implements TaskAppService {

    // infra
    private final TaskDao taskDao;
    private final FileDao fileDao;
    private final TaskConverter taskConverter;
    private final BizFileOssClient bizFileOssClient;
    // domainservice
    private final TaskService taskService;
    private final FileService fileService;

    @Override
    public PageResponse<TaskDTO> pageTasks(TaskPageQry qry) {
        Page<TaskDO> page = taskDao.pageBy(qry);
        List<TaskDTO> records = taskConverter.doToDto(page.getRecords());
        return PageResponse.of(records, page.getTotalRow(), page.getPageSize(), page.getPageNumber());
    }

    @Override
    public void taskTimeout(int pageIndex, int pageSize) {
        Page<TaskDO> tasks = taskDao.pageRunningTask(pageIndex, pageSize);
        tasks.getRecords().stream()
                .filter(taskService::isTaskTimeout)
                .forEach(task -> taskDao.updateTaskStatus(task.getId(), TaskStatusEnum.TIMEOUT));
    }

    @Override
    public String createFileUrl(String taskId) {
        TaskDO task = taskDao.getByTaskId(taskId)
                .orElseThrow(() -> new IllegalArgumentException("任务不存在 " + taskId));
        FileDO file = fileDao.getByFileId(task.getFileId())
                .orElseThrow(() -> new IllegalArgumentException("任务文件不存在 " + task.getFileId()));
        return fileService.createFileUrl(file.getFileId(), "/api/task/download-file");
    }

    @Override
    public ResponseEntity<Resource> downloadTaskFile(String fileToken) {
        FileDO file = fileService.getFileByToken(fileToken);
        return FileDownloader.buildResourceResponseEntity(
                file.getFileName(),
                file.getFileType(),
                FileDownloader.ContentDisposition.ATTACHMENT,
                () -> bizFileOssClient.getObject(file.getObjectName())
        );
    }

}
