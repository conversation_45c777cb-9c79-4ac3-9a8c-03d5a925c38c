package com.xtc.marketing.invoiceservice.invoice.exceldto;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品导出 DTO
 */
@Getter
@Setter
@ToString
public class GoodsExportDTO {

    /**
     * 唯一标识
     */
    @ExcelIgnore
    private Long id;
    /**
     * 物料代码
     */
    @ExcelProperty("物料代码")
    private String erpCode;
    /**
     * 物料名称
     */
    @ExcelProperty("物料名称")
    private String erpName;
    /**
     * 商品名称
     */
    @ExcelProperty("商品名称")
    private String goodsName;
    /**
     * 税收分类编码
     */
    @ExcelProperty("税收分类编码")
    private String taxClassificationCode;
    /**
     * 税务名称
     */
    @ExcelProperty("税务名称")
    private String taxItemName;
    /**
     * 税率（13%=0.13）
     */
    @ExcelProperty("税率")
    private BigDecimal taxRate;
    /**
     * 规格型号
     */
    @ExcelProperty("规格型号")
    private String specification;
    /**
     * 单位
     */
    @ExcelProperty("单位")
    private String unit;
    /**
     * 更新时间
     */
    @ExcelProperty("更新时间")
    private LocalDateTime updateTime;
    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
