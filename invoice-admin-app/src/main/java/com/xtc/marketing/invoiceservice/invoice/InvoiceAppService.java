package com.xtc.marketing.invoiceservice.invoice;

import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceApplyDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceItemDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceExportCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceApplyPageQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceFileAdminGetQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceItemListQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoicePageQry;

import java.util.List;

/**
 * 发票应用服务接口
 */
public interface InvoiceAppService {

    /**
     * 发票申请分页列表
     *
     * @param qry 参数
     * @return 发票申请分页列表
     */
    PageResponse<InvoiceApplyDTO> pageInvoiceApplies(InvoiceApplyPageQry qry);

    /**
     * 发票分页列表
     *
     * @param qry 参数
     * @return 发票分页列表
     */
    PageResponse<InvoiceDTO> pageInvoices(InvoicePageQry qry);

    /**
     * 发票项目列表
     *
     * @param qry 参数
     * @return 发票项目列表
     */
    List<InvoiceItemDTO> listInvoiceItems(InvoiceItemListQry qry);

    /**
     * 发票文件地址
     *
     * @param qry 参数
     * @return 发票文件地址
     */
    String invoiceFile(InvoiceFileAdminGetQry qry);

    /**
     * 发票导出
     *
     * @param cmd 参数
     */
    void exportInvoice(InvoiceExportCmd cmd);

}
