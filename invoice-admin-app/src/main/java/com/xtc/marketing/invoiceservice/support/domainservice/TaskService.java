package com.xtc.marketing.invoiceservice.support.domainservice;

import com.xtc.marketing.invoiceservice.config.BaseDO;
import com.xtc.marketing.invoiceservice.constant.SystemConstant;
import com.xtc.marketing.invoiceservice.context.UserContext;
import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.oss.BizFileOssClient;
import com.xtc.marketing.invoiceservice.oss.cosntant.BizFileOssConstant;
import com.xtc.marketing.invoiceservice.support.context.TaskContext;
import com.xtc.marketing.invoiceservice.support.dao.FileDao;
import com.xtc.marketing.invoiceservice.support.dao.TaskDao;
import com.xtc.marketing.invoiceservice.support.dataobject.FileDO;
import com.xtc.marketing.invoiceservice.support.dataobject.TaskDO;
import com.xtc.marketing.invoiceservice.support.enums.TaskStatusEnum;
import com.xtc.marketing.invoiceservice.util.DateUtil;
import com.xtc.marketing.invoiceservice.util.FastExcelUtil;
import com.xtc.marketing.invoiceservice.util.GsonUtil;
import com.xtc.marketing.invoiceservice.util.UuidGenerator;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 任务领域服务
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class TaskService {

    /**
     * 任务最大并发数
     */
    private static final int MAX_CONCURRENT = 50;
    /**
     * 默认分片处理数据量
     */
    private static final int DEFAULT_SHARD_SIZE = 50;
    /**
     * 默认分批导出数据量
     */
    private static final int DEFAULT_EXPORT_BATCH_SIZE = 200;
    /**
     * 单个用户最大任务数
     */
    private static final int TASK_PER_USER_LIMIT = 10;
    /**
     * 限制任务处理的数据量
     */
    private static final int TASK_DATA_LIMIT = 1000000;
    /**
     * 任务超时时间
     */
    private static final Duration TASK_TIMEOUT = Duration.ofMinutes(5);
    /**
     * OSS 对象名格式：任务名/日期/用户id/文件名
     */
    private static final String OSS_OBJECT_NAME_FORMAT = "%s/%s/%s/%s";

    // infra
    private final TaskDao taskDao;
    private final FileDao fileDao;
    private final BizFileOssClient bizFileOssClient;
    private final PlatformTransactionManager transactionManager;

    /**
     * 虚拟线程执行器
     */
    private ExecutorService virtualExecutor;

    @PostConstruct
    public void init() {
        // 初始化虚拟线程执行器
        ThreadFactory virtualThreadFactory = Thread.ofVirtual().name("task-", 0).factory();
        virtualExecutor = Executors.newThreadPerTaskExecutor(virtualThreadFactory);
    }

    @PreDestroy
    public void shutdown() {
        // 关闭虚拟线程执行器
        if (virtualExecutor != null && !virtualExecutor.isShutdown()) {
            virtualExecutor.shutdown();
        }
    }

    /**
     * 判断任务已超时
     *
     * @param task 任务
     * @return 执行结果
     */
    public boolean isTaskTimeout(TaskDO task) {
        if (task == null || task.getTaskStatus() == null || task.getUpdateTime() == null) {
            return false;
        }
        if (task.getTaskStatus() == TaskStatusEnum.TIMEOUT) {
            return true;
        }
        if (task.getTaskStatus() != TaskStatusEnum.RUNNING) {
            return false;
        }
        Duration duration = Duration.between(task.getUpdateTime(), LocalDateTime.now());
        return duration.toSeconds() >= TASK_TIMEOUT.toSeconds();
    }

    /**
     * 提交导出任务
     *
     * @param taskName    任务名称
     * @param request     请求参数
     * @param total       数据总数
     * @param exportClass 导出数据类型
     * @param batchData   批量数据提供函数
     * @param <R>         请求参数类型
     * @param <T>         导出数据类型
     */
    public <R, T> void submitExportTask(String taskName, R request, long total, Class<T> exportClass, Function<T, List<T>> batchData) {
        // 创建导出临时文件
        Path tempFile = this.createTempFile(taskName);
        log.info("总行数 {} 生成临时导出文件 {}", total, tempFile);
        // 提交任务
        this.submitTask(
                taskName,
                request,
                total,
                DEFAULT_EXPORT_BATCH_SIZE,
                (taskContext, jobRuntime) -> {
                    // 将临时文件路径设置到任务上下文中管理，后续任务完成后清理
                    taskContext.setTempFile(tempFile);
                    // 提交虚拟线程任务
                    virtualExecutor.execute(() -> jobRuntime.jobExecutor(runtime -> {
                        // 更新任务状态为运行中
                        taskDao.updateTaskStatus(taskContext.getId(), TaskStatusEnum.RUNNING);
                        log.info("开始导出任务 {} {}", taskContext.getId(), TaskStatusEnum.RUNNING);
                        // 写入数据到文件
                        FastExcelUtil.writeExcelBatch(
                                taskContext.getTempFile(),
                                taskContext.getTotal(),
                                exportClass,
                                lastData -> {
                                    List<T> data = batchData.apply(lastData);
                                    // 增加计数
                                    long syncedCount = runtime.getCounter().addAndGet(data.size());
                                    this.updateTaskProcessedCount(taskContext, syncedCount);
                                    return data;
                                }
                        );
                        // 上传导出文件到 OSS
                        this.uploadExportFile(taskContext);
                        log.info("导出任务完成 {}", taskContext.getId());
                        // 清理任务上下文中的临时文件
                        taskContext.cleanupTempFile();
                    }));
                }
        );
    }

    /**
     * 提交导入任务
     *
     * @param taskName    任务名称
     * @param file        导入文件
     * @param importClass 导入数据类型
     * @param bizConsumer 业务处理逻辑
     * @param <T>         导入数据类型
     */
    public <T> void submitImportTask(String taskName, MultipartFile file, Class<T> importClass, Consumer<T> bizConsumer) {
        // 导入文件保存为临时文件方便后续多次读取
        Path tempFile = this.saveTempFile(taskName, file);
        // 统计 excel 总行数
        long total = FastExcelUtil.excelTotalRow(tempFile);
        log.info("总行数 {} 生成临时导入文件 {}", total, tempFile);
        // 提交任务
        this.submitTask(
                taskName,
                null,
                total,
                FastExcelUtil.DEFAULT_BATCH_COUNT,
                (taskContext, jobRuntime) -> {
                    // 将临时文件路径设置到任务上下文中管理，后续任务完成后清理
                    taskContext.setTempFile(tempFile);
                    // 提交虚拟线程任务
                    virtualExecutor.execute(() -> jobRuntime.jobExecutor(runtime -> {
                        // 更新任务状态为运行中
                        taskDao.updateTaskStatus(taskContext.getId(), TaskStatusEnum.RUNNING);
                        log.info("开始导入任务 {} {}", taskContext.getId(), TaskStatusEnum.RUNNING);
                        // 从临时文件读取数据并处理
                        byte[] errorFileBytes;
                        try (InputStream fileStream = Files.newInputStream(taskContext.getTempFile())) {
                            errorFileBytes = FastExcelUtil.readExcelWithTransactionThenReturnByte(
                                    fileStream,
                                    transactionManager,
                                    importClass,
                                    new FastExcelUtil.BizConsumer<>("import-task", data -> {
                                        bizConsumer.accept(data);
                                        // 增加计数
                                        long syncedCount = runtime.getCounter().addAndGet(1);
                                        // 随机更新任务进度
                                        this.updateTaskRandomProgress(taskContext, syncedCount);
                                    })
                            );
                        } catch (Exception e) {
                            log.error("导入任务文件读取失败 {}", taskContext.getId(), e);
                            taskDao.updateTaskStatus(taskContext.getId(), TaskStatusEnum.FAILED);
                            this.updateTaskProcessedCount(taskContext, runtime.getCounter().get());
                            return;
                        }
                        // 上传导入异常数据文件到 OSS
                        this.uploadImportErrorFile(taskContext, errorFileBytes);
                        // 更新任务状态为完成，可能会存在导入成功的数量小于总数的情况
                        taskDao.updateTaskStatus(taskContext.getId(), TaskStatusEnum.COMPLETED);
                        this.updateTaskProcessedCount(taskContext, runtime.getCounter().get());
                        log.info("导入任务完成 {}", taskContext.getId());
                        // 清理任务上下文中的临时文件
                        taskContext.cleanupTempFile();
                    }));
                }
        );
    }

    /**
     * 提交分片任务
     *
     * @param taskName         任务名称
     * @param request          请求参数
     * @param total            数据总数
     * @param pageDataSupplier 分页数据提供函数
     * @param dataConsumer     数据消费函数
     * @param <R>              请求参数类型
     * @param <T>              数据类型
     */
    public <R, T> void submitShardTask(String taskName, R request, long total,
                                       Function<TaskContext<R>, List<T>> pageDataSupplier,
                                       Consumer<List<T>> dataConsumer) {
        this.submitTask(
                taskName,
                request,
                total,
                DEFAULT_SHARD_SIZE,
                (taskContext, jobRuntime) -> {
                    // 遍历所有页同步数据
                    for (int index = 1; index <= taskContext.getPages(); index++) {
                        // 设置任务上下文页码
                        taskContext.setPageIndex(index);
                        // 提交虚拟线程任务
                        virtualExecutor.execute(() -> jobRuntime.jobExecutor(runtime -> {
                            taskDao.updateTaskStatus(taskContext.getId(), TaskStatusEnum.RUNNING);
                            // 获取分页数据
                            List<T> data = pageDataSupplier.apply(taskContext);
                            // 增加计数
                            long syncedCount = runtime.getCounter().addAndGet(data.size());
                            this.updateTaskProcessedCount(taskContext, syncedCount);
                            // 处理数据
                            dataConsumer.accept(data);
                        }));
                    }
                }
        );
    }

    /**
     * 提交任务
     *
     * @param taskName   任务名称
     * @param request    请求参数
     * @param total      数据总数
     * @param pageSize   分页数据量
     * @param jobExecute 任务执行函数
     * @param <R>        请求参数类型
     */
    public <R> void submitTask(String taskName, R request, long total, int pageSize, BiConsumer<TaskContext<R>, JobRuntime> jobExecute) {
        // 检查允许提交任务
        this.checkSubmitTask(total);
        // 创建任务上下文
        TaskContext<R> taskContext = this.createTaskContext(taskName, request, total, pageSize);
        // 数据总数为 0 则不需要处理，直接完成任务
        if (taskContext.getTotal() <= 0) {
            throw BizException.of("任务没有数据需要处理");
        }
        // 创建任务
        TaskDO task = this.createTask(taskName, taskContext);
        taskContext.setId(task.getId());
        log.info("开始任务处理 {}", taskContext);
        try {
            // 初始化任务运行时，设置任务处理并发数
            JobRuntime jobRuntime = new JobRuntime(MAX_CONCURRENT);
            // 提交虚拟线程任务
            jobExecute.accept(taskContext, jobRuntime);
        } catch (Exception e) {
            log.error("任务失败 {}", taskContext.getId(), e);
            taskDao.updateTaskStatus(taskContext.getId(), TaskStatusEnum.FAILED);
        }
    }

    /**
     * 检查允许提交任务
     *
     * @param total 数据总数
     */
    private void checkSubmitTask(long total) {
        if (total > TASK_DATA_LIMIT) {
            throw BizException.of("任务数据量超过限制，请拆分任务 [最大允许 %s 条数据]", TASK_DATA_LIMIT);
        }
        UserContext.User user = UserContext.getUser();
        if (user != null) {
            boolean isTaskCountExceedLimit = taskDao.isTaskCountExceedLimit(user.getEmployeeId(), TASK_PER_USER_LIMIT);
            if (isTaskCountExceedLimit) {
                throw BizException.of("您有任务正在处理中，请等待任务完成后再提交新任务");
            }
        }
    }

    /**
     * 上传导出文件到 OSS
     *
     * @param taskContext 任务上下文
     */
    private void uploadExportFile(TaskContext<?> taskContext) {
        String fileName = taskContext.getTempFile().getFileName().toString();
        String objectName = OSS_OBJECT_NAME_FORMAT.formatted(taskContext.getTaskName(),
                DateUtil.toString(taskContext.getCreateTime(), DateUtil.FORMAT_DATE_COMPACT), taskContext.getUserId(), fileName);
        String fullObjectName = BizFileOssConstant.buildExportObjectName(objectName);
        try {
            // 上传文件到 oss
            bizFileOssClient.putObject(fullObjectName, taskContext.getTempFile().toFile(), "text/csv");
            // 更新任务文件
            String fileId = this.saveExportFile(fileName, fullObjectName);
            taskDao.updateTaskFile(taskContext.getId(), fileId);
            log.info("导出文件上传成功 {}", fullObjectName);
        } catch (Exception e) {
            log.info("导出文件上传失败 {} {}", fullObjectName, e.getCause().getMessage(), e.getCause());
        }
    }

    /**
     * 上传导入异常数据文件到 OSS
     *
     * @param taskContext 任务上下文
     * @param fileBytes   异常数据文件字节数组
     */
    private void uploadImportErrorFile(TaskContext<Object> taskContext, byte[] fileBytes) {
        if (fileBytes.length == 0) {
            return;
        }
        String datePath = DateUtil.toString(taskContext.getCreateTime(), DateUtil.FORMAT_DATE_COMPACT);
        String errorFileName = "error_" + taskContext.getTempFile().getFileName().toString();
        String errorObjectName = OSS_OBJECT_NAME_FORMAT.formatted(taskContext.getTaskName(), datePath, taskContext.getUserId(), errorFileName);
        String fullObjectName = BizFileOssConstant.buildImportObjectName(errorObjectName);
        try {
            // 上传异常数据文件到 OSS
            bizFileOssClient.putObject(fullObjectName, fileBytes,
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            // 更新任务文件
            String fileId = this.saveExportFile(errorFileName, fullObjectName);
            taskDao.updateTaskFile(taskContext.getId(), fileId);
            log.info("导入异常数据文件已上传 {}", fullObjectName);
        } catch (Exception e) {
            log.warn("导入异常数据文件上传失败 {} {}", fullObjectName, e.getCause().getMessage(), e.getCause());
        }
    }

    /**
     * 保存导出文件
     *
     * @param fileName   文件名
     * @param objectName 对象名
     * @return 文件id
     */
    private String saveExportFile(String fileName, String objectName) {
        String fileType = fileName.substring(fileName.lastIndexOf('.') + 1);
        FileDO file = FileDO.builder()
                .fileName(fileName)
                .objectName(objectName)
                .fileType(fileType)
                .build();
        fileDao.save(file);
        return file.getFileId();
    }

    /**
     * 随机更新任务进度
     *
     * @param taskContext    任务上下文
     * @param processedCount 已处理数量
     */
    private void updateTaskRandomProgress(TaskContext<?> taskContext, long processedCount) {
        // 计算新增数量
        long newAddedCount = processedCount - taskContext.getLastProcessedCount();
        // 使用随机百分比计算更新阈值，取整数
        long updateThreshold = Math.max(1, taskContext.getTotal() * taskContext.getRandomPercent() / 100);
        // 当新增数量达到阈值时才更新
        if (newAddedCount >= updateThreshold) {
            // 更新任务已处理数量
            this.updateTaskProcessedCount(taskContext, processedCount);
            // 更新上下文数据
            taskContext.setLastProcessedCount(processedCount);
            taskContext.generateNewRandomPercent();
        }
    }

    /**
     * 更新任务已处理数量
     *
     * @param taskContext    任务上下文
     * @param processedCount 已处理数量
     */
    private void updateTaskProcessedCount(TaskContext<?> taskContext, long processedCount) {
        if (processedCount <= 0 || taskContext.getTotal() <= 0) {
            return;
        }
        TaskDO updateTask = new TaskDO();
        updateTask.setProcessedCount(processedCount);
        // 计算同步进度百分比
        long progressPercentage = Math.round(processedCount * 100.0 / taskContext.getTotal());
        updateTask.setProgressPercentage(progressPercentage + "%");
        // 判断同步完成
        if (processedCount >= taskContext.getTotal()) {
            updateTask.setTaskStatus(TaskStatusEnum.COMPLETED);
        }
        taskDao.updateById(taskContext.getId(), updateTask);
        log.info("更新任务已处理数量 {} {} {}", updateTask.getProcessedCount(), updateTask.getProgressPercentage(),
                updateTask.getTaskStatus() != null ? updateTask.getTaskStatus() : StringUtils.EMPTY);
    }

    /**
     * 创建任务
     *
     * @param taskName    任务名称
     * @param taskContext 任务上下文
     * @param <R>         请求参数类型
     * @return 任务
     */
    private <R> TaskDO createTask(String taskName, TaskContext<R> taskContext) {
        // 创建任务
        TaskDO task = new TaskDO();
        task.setTaskName(taskName);
        task.setTaskStatus(TaskStatusEnum.PENDING);
        task.setRequestParamsJson(taskContext.getRequest() != null ? GsonUtil.objectToJson(taskContext.getRequest()) : null);
        task.setTotal(taskContext.getTotal());
        task.setProcessedCount(0L);
        task.setProgressPercentage("0%");
        // 设置时间
        task.setCreateTime(taskContext.getCreateTime());
        task.setUpdateTime(taskContext.getCreateTime());
        // 设置任务id
        String taskId = UuidGenerator.randomUuid();
        task.setTaskId(taskId);
        // 设置日志id
        String traceId = StringUtils.defaultIfBlank(MDC.get(SystemConstant.MDC_TRACE_ID), taskId);
        task.setLogId(traceId);
        // 数据总数为 0 则不需要处理，直接完成任务
        if (taskContext.getTotal() <= 0) {
            task.setTaskStatus(TaskStatusEnum.COMPLETED);
            task.setProgressPercentage("100%");
        }
        // 设置创建人
        UserContext.User user = UserContext.getUser();
        BaseDO.Operator creator = user != null ? BaseDO.Operator.of(user.getEmployeeId(), user.getUserName()) : BaseDO.Operator.ofSystem();
        task.setCreateBy(creator);
        // 保存数据
        taskDao.save(task);
        return task;
    }

    /**
     * 创建任务上下文
     *
     * @param taskName 任务名称
     * @param request  请求参数
     * @param total    数据总数
     * @param pageSize 分页数据量
     * @param <R>      请求参数类型
     * @return 任务上下文
     */
    private <R> TaskContext<R> createTaskContext(String taskName, R request, long total, int pageSize) {
        TaskContext<R> taskContext = TaskContext.<R>builder()
                .taskName(taskName)
                .userId(UserContext.getUser().getUserId())
                .createTime(LocalDateTime.now())
                .total(total)
                .pages(0)
                .pageSize(pageSize)
                .pageIndex(0)
                .request(request)
                .lastProcessedCount(0L)
                .build();
        // 初始化随机百分比
        taskContext.generateNewRandomPercent();
        if (taskContext.getTotal() <= 0) {
            return taskContext;
        }
        // 计算总页数
        int pages = (int) (taskContext.getTotal() / taskContext.getPageSize());
        // 如果总数不能被分页大小整除，则需要多一页
        if (taskContext.getTotal() % taskContext.getPageSize() > 0) {
            pages++;
        }
        taskContext.setPages(pages);
        return taskContext;
    }

    /**
     * 将文件临时保存到本地，方便后续多次读取
     * <p>/logs/temp-file/taskName/datePath/userId/tempFile.fileSuffix</p>
     *
     * @param taskName 任务名称
     * @param file     上传的文件
     * @return 临时文件的路径
     */
    private Path saveTempFile(String taskName, MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw BizException.of("文件不能为空");
        }
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isBlank(originalFilename)) {
            throw BizException.of("文件名不能为空");
        }
        String fileSuffix = originalFilename.substring(originalFilename.lastIndexOf('.'));
        Path tempDir = Path.of("logs", "temp-file", taskName,
                DateUtil.nowDateCompact(), UserContext.getUser().getUserId());
        try {
            // 创建目录
            Files.createDirectories(tempDir);
            // 创建临时文件
            Path tempFile = Files.createTempFile(tempDir, null, fileSuffix);
            // 使用 try-with-resources 确保流被关闭
            try (InputStream fileStream = file.getInputStream();
                 OutputStream outputStream = Files.newOutputStream(tempFile)) {
                fileStream.transferTo(outputStream);
            }
            return tempFile;
        } catch (Exception e) {
            throw BizException.of("保存临时文件失败", e);
        }
    }

    /**
     * 生成临时文件
     *
     * @param taskName 任务名称
     * @return 临时文件路径
     */
    private Path createTempFile(String taskName) {
        // 初始化文件路径：/taskName/datePath/userId/taskName_yyyyMMdd_HHmmss_SSS.csv
        LocalDateTime now = LocalDateTime.now();
        String datePath = DateUtil.toString(now, DateUtil.FORMAT_DATE_COMPACT);
        String fileName = "%s_%s.csv".formatted(taskName, DateUtil.toString(now, "yyyyMMdd_HHmmss_SSS"));
        String objectName = OSS_OBJECT_NAME_FORMAT.formatted(taskName, datePath, UserContext.getUser().getUserId(), fileName);
        String tempFilePath = "logs/temp-file/%s".formatted(objectName);
        // 创建临时文件
        File file = new File(tempFilePath);
        if (!file.getParentFile().exists()) {
            boolean initFolder = file.getParentFile().mkdirs();
            if (!initFolder) {
                throw BizException.of("创建导出文件目录失败");
            }
        }
        return Path.of(file.toURI());
    }

    /**
     * 任务运行时
     */
    @Getter
    @Setter
    @ToString
    public static class JobRuntime {

        /**
         * 全链路跟踪id
         */
        private String traceId;
        /**
         * 并发信号量
         */
        private Semaphore semaphore;
        /**
         * 计数器
         */
        private AtomicLong counter;

        /**
         * 生成任务运行时
         *
         * @param maxConcurrent 最大并发数
         */
        JobRuntime(int maxConcurrent) {
            this.traceId = MDC.get(SystemConstant.MDC_TRACE_ID);
            this.semaphore = new Semaphore(maxConcurrent);
            this.counter = new AtomicLong(0);
        }

        /**
         * 任务执行器
         *
         * @param job 任务
         */
        private void jobExecutor(Consumer<JobRuntime> job) {
            boolean acquireSemaphore = false;
            // 设置虚拟线程执行id
            String executeId = UuidGenerator.randomUuid();
            try (MDC.MDCCloseable ignored = MDC.putCloseable(SystemConstant.MDC_EXECUTE_ID, executeId);
                 MDC.MDCCloseable ignored2 = MDC.putCloseable(SystemConstant.MDC_TRACE_ID, this.traceId)) {
                // 获取信号量
                acquireSemaphore = this.acquireSemaphore();
                // 处理数据
                if (acquireSemaphore) {
                    try {
                        // 执行任务
                        job.accept(this);
                    } catch (Exception e) {
                        log.warn("任务失败 message: {}", e.getMessage(), e);
                    }
                }
            } finally {
                // 释放信号量
                if (acquireSemaphore) {
                    this.releaseSemaphore();
                }
            }
        }

        /**
         * 获取信号量
         *
         * @return 执行结果
         */
        public boolean acquireSemaphore() {
            try {
                this.semaphore.acquire();
                return true;
            } catch (InterruptedException e) {
                log.warn("获取信号量许可被中断", e);
                Thread.currentThread().interrupt();
                return false;
            }
        }

        /**
         * 释放信号量
         */
        public void releaseSemaphore() {
            this.semaphore.release();
        }

    }

}
