package com.xtc.marketing.invoiceservice.support;

import com.mybatisflex.core.paginate.Page;
import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.invoiceservice.exception.BizErrorCode;
import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.support.converter.UserConverter;
import com.xtc.marketing.invoiceservice.support.dao.UserDao;
import com.xtc.marketing.invoiceservice.support.dataobject.UserDO;
import com.xtc.marketing.invoiceservice.support.dto.UserDTO;
import com.xtc.marketing.invoiceservice.support.dto.command.UserCreateCmd;
import com.xtc.marketing.invoiceservice.support.dto.command.UserEditCmd;
import com.xtc.marketing.invoiceservice.support.dto.query.UserPageQry;
import com.xtc.marketing.invoiceservice.util.BeanCopier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
 * 用户应用服务实现类
 * <p>简单逻辑 10 行以内的代码可以直接在 service 方法内实现</p>
 * <p>复杂逻辑（或可复用逻辑）封装在 executor 里实现，调用链路：service > executor</p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserAppServiceImpl implements UserAppService {

    // 基础设施注入
    private final UserDao userDao;
    private final UserConverter userConverter;

    @Override
    public PageResponse<UserDTO> pageUsers(UserPageQry qry) {
        Page<UserDO> page = userDao.pageBy(qry);
        List<UserDTO> records = userConverter.doToDto(page.getRecords());
        return PageResponse.of(records, page.getTotalRow(), page.getPageSize(), page.getPageNumber());
    }

    @Override
    public UserDTO userDetail(long id) {
        UserDO userDO = userDao.getByIdOpt(id)
                .orElseThrow(() -> BizException.of(BizErrorCode.B_USER_UserNotExists));
        return userConverter.doToDto(userDO);
    }

    @Override
    public Long createUser(UserCreateCmd cmd) {
        // 校验工号不能重复
        boolean existsByEmployeeId = userDao.existsByEmployeeId(cmd.getEmployeeId());
        if (existsByEmployeeId) {
            throw BizException.of("工号已存在");
        }
        // 新增用户
        UserDO userDO = BeanCopier.copy(cmd, UserDO::new);
        userDO.setUserId(UUID.randomUUID().toString().replace("-", ""));
        userDao.save(userDO);
        return userDO.getId();
    }

    @Override
    public void editUser(long id, UserEditCmd cmd) {
        // 校验用户是否存在
        boolean notExists = userDao.getByIdOpt(id).isEmpty();
        if (notExists) {
            throw BizException.of(BizErrorCode.B_USER_UserNotExists);
        }
        // 校验工号不能重复
        if (StringUtils.isNotBlank(cmd.getEmployeeId())) {
            boolean existsByEmployeeId = userDao.existsByEmployeeIdExcludeId(cmd.getEmployeeId(), id);
            if (existsByEmployeeId) {
                throw BizException.of("工号已存在");
            }
        }
        // 编辑用户
        UserDO userDO = BeanCopier.copy(cmd, UserDO::new);
        userDao.updateById(id, userDO);
    }

    @Override
    public void removeUser(long id) {
        // 参数校验
        boolean notExists = userDao.getByIdOpt(id).isEmpty();
        if (notExists) {
            throw BizException.of(BizErrorCode.B_USER_UserNotExists);
        }
        // 删除用户
        userDao.removeById(id);
    }

}
