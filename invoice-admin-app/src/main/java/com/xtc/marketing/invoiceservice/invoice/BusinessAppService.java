package com.xtc.marketing.invoiceservice.invoice;

import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.invoiceservice.invoice.dto.BusinessDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.BusinessCreateCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.BusinessEditCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.BusinessPageQry;

/**
 * 业务接入应用服务接口
 */
public interface BusinessAppService {

    /**
     * 业务接入分页列表
     *
     * @param qry 参数
     * @return 业务接入分页列表
     */
    PageResponse<BusinessDTO> pageBusinesses(BusinessPageQry qry);

    /**
     * 业务接入详情
     *
     * @param id 业务接入id
     * @return 业务接入详情
     */
    BusinessDTO businessDetail(long id);

    /**
     * 新增业务接入
     *
     * @param cmd 参数
     * @return 业务接入id
     */
    Long createBusiness(BusinessCreateCmd cmd);

    /**
     * 编辑业务接入
     *
     * @param id  业务接入id
     * @param cmd 参数
     */
    void editBusiness(long id, BusinessEditCmd cmd);

    /**
     * 删除业务接入
     *
     * @param id 业务接入id
     */
    void removeBusiness(long id);

}
