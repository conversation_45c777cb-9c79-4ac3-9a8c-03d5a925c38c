package com.xtc.marketing.invoiceservice.invoice;

import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.invoiceservice.config.BaseJobBO;
import com.xtc.marketing.invoiceservice.invoice.dto.GoodsDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.GoodsCreateCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.GoodsEditCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.GoodsPageQry;
import org.springframework.web.multipart.MultipartFile;

/**
 * 商品应用服务
 */
public interface GoodsAppService {

    /**
     * 查询商品分页列表
     *
     * @param qry 参数
     * @return 商品分页列表
     */
    PageResponse<GoodsDTO> pageGoods(GoodsPageQry qry);

    /**
     * 查询商品详情
     *
     * @param id 商品id
     * @return 商品详情
     */
    GoodsDTO goodsDetail(long id);

    /**
     * 新增商品
     *
     * @param cmd 参数
     * @return 商品id
     */
    Long createGoods(GoodsCreateCmd cmd);

    /**
     * 修改商品
     *
     * @param id  商品id
     * @param cmd 参数
     */
    void editGoods(long id, GoodsEditCmd cmd);

    /**
     * 删除商品
     *
     * @param id 商品id
     */
    void removeGoods(long id);

    /**
     * 商品导出任务
     */
    void exportGoods();

    /**
     * 导入商品
     *
     * @param file 文件
     */
    void importGoods(MultipartFile file);

    /**
     * 商品同步任务
     *
     * @param shardIndex 分片索引
     * @param jobParam   任务参数
     */
    void goodsSyncJob(int shardIndex, BaseJobBO jobParam);

}
