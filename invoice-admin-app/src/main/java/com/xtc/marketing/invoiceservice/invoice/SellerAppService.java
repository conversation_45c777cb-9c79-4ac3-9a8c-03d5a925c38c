package com.xtc.marketing.invoiceservice.invoice;

import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.invoiceservice.invoice.dto.SellerDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.SellerCreateCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.SellerEditCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.SellerPageQry;

/**
 * 销售方应用服务
 */
public interface SellerAppService {

    /**
     * 查询销售方分页列表
     *
     * @param qry 参数
     * @return 销售方分页列表
     */
    PageResponse<SellerDTO> pageSellers(SellerPageQry qry);

    /**
     * 查询销售方详情
     *
     * @param id 销售方id
     * @return 销售方详情
     */
    SellerDTO sellerDetail(long id);

    /**
     * 新增销售方
     *
     * @param cmd 参数
     * @return 销售方id
     */
    Long createSeller(SellerCreateCmd cmd);

    /**
     * 修改销售方
     *
     * @param id  销售方id
     * @param cmd 参数
     */
    void editSeller(long id, SellerEditCmd cmd);

    /**
     * 删除销售方
     *
     * @param id 销售方id
     */
    void removeSeller(long id);

}
