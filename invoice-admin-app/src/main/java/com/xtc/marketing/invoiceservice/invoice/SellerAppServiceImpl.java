package com.xtc.marketing.invoiceservice.invoice;

import com.mybatisflex.core.paginate.Page;
import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.invoiceservice.exception.BizErrorCode;
import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.invoice.converter.SellerConverter;
import com.xtc.marketing.invoiceservice.invoice.dao.SellerDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.SellerDO;
import com.xtc.marketing.invoiceservice.invoice.dto.SellerDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.SellerCreateCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.SellerEditCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.SellerPageQry;
import com.xtc.marketing.invoiceservice.util.BeanCopier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 销售方应用服务实现类
 * <p>简单逻辑 10 行以内的代码可以直接在 service 方法内实现</p>
 * <p>复杂逻辑（或可复用逻辑）封装在 executor 里实现，调用链路：service > executor</p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SellerAppServiceImpl implements SellerAppService {

    // 基础设施注入
    private final SellerDao sellerDao;
    private final SellerConverter sellerConverter;

    @Override
    public PageResponse<SellerDTO> pageSellers(SellerPageQry qry) {
        Page<SellerDO> page = sellerDao.pageBy(qry);
        List<SellerDTO> records = sellerConverter.doToDto(page.getRecords());
        return PageResponse.of(records, page.getTotalRow(), page.getPageSize(), page.getPageNumber());
    }

    @Override
    public SellerDTO sellerDetail(long id) {
        SellerDO sellerDO = sellerDao.getByIdOpt(id)
                .orElseThrow(() -> BizException.of(BizErrorCode.B_SELLER_SellerNotExists));
        return sellerConverter.doToDto(sellerDO);
    }

    @Override
    public Long createSeller(SellerCreateCmd cmd) {
        // 参数校验
        boolean existsBySellerCode = sellerDao.existsBySellerCode(cmd.getSellerCode());
        if (existsBySellerCode) {
            throw BizException.of("销售方代码已存在");
        }
        // 新增销售方
        SellerDO sellerDO = BeanCopier.copy(cmd, SellerDO::new);
        sellerDao.save(sellerDO);
        return sellerDO.getId();
    }

    @Override
    public void editSeller(long id, SellerEditCmd cmd) {
        // 参数校验
        boolean notExists = sellerDao.getByIdOpt(id).isEmpty();
        if (notExists) {
            throw BizException.of(BizErrorCode.B_SELLER_SellerNotExists);
        }
        if (StringUtils.isNotBlank(cmd.getSellerCode())) {
            boolean existsBySellerCode = sellerDao.existsByCodeExcludeId(cmd.getSellerCode(), id);
            if (existsBySellerCode) {
                throw BizException.of("销售方代码已存在");
            }
        }
        // 编辑销售方
        SellerDO sellerDO = BeanCopier.copy(cmd, SellerDO::new);
        sellerDao.updateById(id, sellerDO);
    }

    @Override
    public void removeSeller(long id) {
        // 参数校验
        boolean notExists = sellerDao.getByIdOpt(id).isEmpty();
        if (notExists) {
            throw BizException.of(BizErrorCode.B_SELLER_SellerNotExists);
        }
        // 删除销售方
        sellerDao.removeById(id);
    }

}
