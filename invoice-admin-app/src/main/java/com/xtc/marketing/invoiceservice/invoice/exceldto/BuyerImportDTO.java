package com.xtc.marketing.invoiceservice.invoice.exceldto;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 购买方导入 DTO
 */
@Getter
@Setter
@ToString
public class BuyerImportDTO {

    /**
     * 购买方代码
     */
    @ExcelProperty("购买方代码")
    private String buyerCode;
    /**
     * 购买方税号
     */
    @ExcelProperty("购买方税号")
    private String buyerIdentifyNo;
    /**
     * 购买方名称
     */
    @ExcelProperty("购买方名称")
    private String buyerName;
    /**
     * 购买方电话
     */
    @ExcelProperty("购买方电话")
    private String buyerPhone;
    /**
     * 购买方银行
     */
    @ExcelProperty("购买方银行")
    private String buyerBankName;
    /**
     * 购买方银行账号
     */
    @ExcelProperty("购买方银行账号")
    private String buyerBankAccount;
    /**
     * 购买方地址
     */
    @ExcelProperty("购买方地址")
    private String buyerAddress;

}
