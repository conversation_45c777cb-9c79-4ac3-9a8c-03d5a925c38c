package com.xtc.marketing.invoiceservice.support.domainservice;

import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.Claim;
import com.xtc.marketing.invoiceservice.constant.SystemConstant;
import com.xtc.marketing.invoiceservice.context.UserContext;
import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.support.dao.FileDao;
import com.xtc.marketing.invoiceservice.support.dataobject.FileDO;
import com.xtc.marketing.invoiceservice.util.DateUtil;
import com.xtc.marketing.invoiceservice.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;

/**
 * 文件领域服务
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class FileService {

    private final FileDao fileDao;

    /**
     * API：获取文件链接（域名 + 文件下载接口 + 过期时间 + 文件凭证）
     */
    private static final String API_GET_FILE = "%s%s?expire=%s&fileToken=%s";
    /**
     * JWT：过期时间
     */
    private static final Duration JWT_EXPIRATION = Duration.ofHours(1);
    /**
     * claim：文件id
     */
    private static final String CLAIM_FILE_ID = "file_id";

    @Value("${spring.profiles.active}")
    private String activeProfile;
    @Value("${xtc.marketing.security.issuer}")
    private String issuer;
    @Value("${xtc.marketing.security.system}")
    private String system;
    @Value("${xtc.marketing.security.private-key}")
    private String privateKey;
    @Value("${xtc.marketing.security.public-key}")
    private String publicKey;

    /**
     * 生成文件链接
     *
     * @param fileId  文件id
     * @param apiPath 文件下载接口
     * @return 文件链接
     */
    public String createFileUrl(String fileId, String apiPath) {
        Optional<FileDO> fileOpt = fileDao.getByFileId(fileId);
        if (fileOpt.isEmpty()) {
            throw BizException.of("生成文件链接失败");
        }
        FileDO file = fileOpt.get();
        // 域名
        String domain = SystemConstant.getSystemDomain(activeProfile);
        // 过期时间
        String expire = DateUtil.toString(LocalDateTime.now().plus(JWT_EXPIRATION), DateUtil.FORMAT_DATE_TIME_COMPACT);
        // 文件id
        Map<String, String> claims = Map.of(
                CLAIM_FILE_ID, file.getFileId(),
                "creator_id", UserContext.getUser().getEmployeeId(),
                "creator_name", UserContext.getUser().getUserName()
        );
        try {
            // 生成文件凭证
            String fileToken = JwtUtil.generateToken(privateKey, issuer, system, JWT_EXPIRATION, claims);
            // 生成文件链接
            return API_GET_FILE.formatted(domain, apiPath, expire, fileToken);
        } catch (Exception e) {
            throw BizException.of("生成文件链接失败 message: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取文件
     *
     * @param fileToken 文件凭证
     * @return 文件
     */
    public FileDO getFileByToken(String fileToken) {
        String fileId;
        // 验证文件凭证，并获取文件id
        try {
            Map<String, Claim> claims = JwtUtil.verifyAndGetClaims(publicKey, issuer, system, fileToken);
            fileId = claims.get(CLAIM_FILE_ID).asString();
        } catch (TokenExpiredException e1) {
            throw BizException.of("文件凭证已过期");
        } catch (Exception e) {
            throw BizException.of("无效的文件凭证");
        }
        // 查询文件
        return fileDao.getByFileId(fileId)
                .orElseThrow(() -> BizException.of("文件不存在"));
    }

}
