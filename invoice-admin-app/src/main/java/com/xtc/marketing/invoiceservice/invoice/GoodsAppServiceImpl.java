package com.xtc.marketing.invoiceservice.invoice;

import com.mybatisflex.core.paginate.Page;
import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.invoiceservice.config.BaseJobBO;
import com.xtc.marketing.invoiceservice.config.BaseJobExe;
import com.xtc.marketing.invoiceservice.context.UserContext;
import com.xtc.marketing.invoiceservice.exception.BizErrorCode;
import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.invoice.converter.GoodsConverter;
import com.xtc.marketing.invoiceservice.invoice.dao.GoodsDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.GoodsDO;
import com.xtc.marketing.invoiceservice.invoice.dto.GoodsDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.GoodsCreateCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.GoodsEditCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.GoodsPageQry;
import com.xtc.marketing.invoiceservice.invoice.exceldto.GoodsExportDTO;
import com.xtc.marketing.invoiceservice.invoice.exceldto.GoodsImportDTO;
import com.xtc.marketing.invoiceservice.rpc.erp.ErpSystemRpc;
import com.xtc.marketing.invoiceservice.rpc.erp.erpdto.request.InvoiceGoodsPageRequest;
import com.xtc.marketing.invoiceservice.rpc.erp.erpdto.response.InvoiceGoodsResponse;
import com.xtc.marketing.invoiceservice.support.domainservice.TaskService;
import com.xtc.marketing.invoiceservice.support.dto.BaseDTO;
import com.xtc.marketing.invoiceservice.util.BeanCopier;
import com.xtc.marketing.invoiceservice.util.CollectionCopier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 商品应用服务实现类
 * <p>简单逻辑 10 行以内的代码可以直接在 service 方法内实现</p>
 * <p>复杂逻辑（或可复用逻辑）封装在 executor 里实现，调用链路：service > executor</p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GoodsAppServiceImpl implements GoodsAppService {

    // 基础设施注入
    private final GoodsDao goodsDao;
    private final GoodsConverter goodsConverter;
    private final ErpSystemRpc erpSystemRpc;
    // domainservice
    private final TaskService taskService;

    @Override
    public PageResponse<GoodsDTO> pageGoods(GoodsPageQry qry) {
        Page<GoodsDO> page = goodsDao.pageBy(qry);
        List<GoodsDTO> records = goodsConverter.doToDto(page.getRecords());
        return PageResponse.of(records, page.getTotalRow(), page.getPageSize(), page.getPageNumber());
    }

    @Override
    public GoodsDTO goodsDetail(long id) {
        GoodsDO goodsDO = goodsDao.getByIdOpt(id)
                .orElseThrow(() -> BizException.of(BizErrorCode.B_GOODS_GoodsNotExists));
        return goodsConverter.doToDto(goodsDO);
    }

    @Override
    public Long createGoods(GoodsCreateCmd cmd) {
        // 参数校验
        boolean existsByErpCode = goodsDao.existsByErpCode(cmd.getErpCode());
        if (existsByErpCode) {
            throw BizException.of("物料代码已存在，无需重复提交");
        }
        // 新增商品
        GoodsDO goodsDO = goodsConverter.cmdToDo(cmd);
        goodsDao.save(goodsDO);
        return goodsDO.getId();
    }

    @Override
    public void editGoods(long id, GoodsEditCmd cmd) {
        // 参数校验
        boolean notExists = goodsDao.getByIdOpt(id).isEmpty();
        if (notExists) {
            throw BizException.of(BizErrorCode.B_GOODS_GoodsNotExists);
        }
        if (StringUtils.isNotBlank(cmd.getErpCode())) {
            boolean existsByErpCode = goodsDao.existsByCodeExcludeId(cmd.getErpCode(), id);
            if (existsByErpCode) {
                throw BizException.of("物料代码已存在，无需重复提交");
            }
        }
        // 编辑商品
        GoodsDO goodsDO = BeanCopier.copy(cmd, GoodsDO::new);
        goodsDao.updateById(id, goodsDO);
    }

    @Override
    public void removeGoods(long id) {
        // 参数校验
        boolean notExists = goodsDao.getByIdOpt(id).isEmpty();
        if (notExists) {
            throw BizException.of(BizErrorCode.B_GOODS_GoodsNotExists);
        }
        // 删除商品
        goodsDao.removeById(id);
    }

    @Override
    public void exportGoods() {
        long total = goodsDao.countForExport();
        taskService.submitExportTask(
                "商品导出",
                null,
                total,
                GoodsExportDTO.class,
                lastData -> {
                    Long lastId = lastData != null ? lastData.getId() : null;
                    List<GoodsDO> goods = goodsDao.listForExport(lastId);
                    return CollectionCopier.copy(goods, GoodsExportDTO::new);
                }
        );
    }

    @Override
    public void importGoods(MultipartFile file) {
        UserContext.User user = UserContext.getUser();
        BaseDTO.OperatorDTO operatorDTO = BaseDTO.OperatorDTO.of(user.getEmployeeId(), user.getUserName());
        taskService.submitImportTask(
                "商品导入",
                file,
                GoodsImportDTO.class,
                dto -> {
                    GoodsCreateCmd cmd = BeanCopier.copy(dto, GoodsCreateCmd::new);
                    cmd.setTaxRate(cmd.getTaxRate().movePointLeft(2));
                    cmd.setUpdateBy(operatorDTO);
                    cmd.setCreateBy(operatorDTO);
                    this.createGoods(cmd);
                });
    }

    @Override
    public void goodsSyncJob(int shardIndex, BaseJobBO jobParam) {
        BaseJobExe.execute(
                jobParam,
                InvoiceGoodsResponse::getItemCode,
                () -> {
                    InvoiceGoodsPageRequest request = new InvoiceGoodsPageRequest();
                    request.setLastUpdateDateFrom(jobParam.getStartTime());
                    request.setLastUpdateDateTo(jobParam.getEndTime());
                    request.setCurrentPage(shardIndex);
                    request.setPageSize(jobParam.getLimit());
                    return erpSystemRpc.pageInvoiceGoods(request).getData();
                },
                data -> {
                    GoodsDO erpGoods = goodsConverter.erpResponseToDo(data);
                    if (StringUtils.isBlank(erpGoods.getErpCode())) {
                        throw BizException.of("物料代码不能为空");
                    }
                    goodsDao.getByErpCode(erpGoods.getErpCode())
                            .ifPresentOrElse(
                                    goods -> goodsDao.updateById(goods.getId(), erpGoods),
                                    () -> goodsDao.save(erpGoods)
                            );
                }
        );
    }

    /**
     * 商品同步任务（使用时间分片和分页功能）
     *
     * @param jobParam 任务参数
     */
    public void goodsSyncJobWithTimeSlicing(BaseJobBO jobParam) {
        BaseJobExe.executeWithTimeShard(
                jobParam,
                InvoiceGoodsResponse::getItemCode,
                // 分页数据获取函数，只传入页码
                pageIndex -> {
                    log.info("获取商品分页数据 - 页码: {}, 时间范围: {} - {}",
                            pageIndex, jobParam.getStartTime(), jobParam.getEndTime());

                    InvoiceGoodsPageRequest request = new InvoiceGoodsPageRequest();
                    request.setLastUpdateDateFrom(jobParam.getStartTime());
                    request.setLastUpdateDateTo(jobParam.getEndTime());
                    request.setCurrentPage(pageIndex);
                    request.setPageSize(100); // 默认分页大小

                    try {
                        return erpSystemRpc.pageInvoiceGoods(request).getData();
                    } catch (Exception e) {
                        log.error("获取商品分页数据失败 - 页码: {}, 异常: {}", pageIndex, e.getMessage());
                        return List.of(); // 返回空列表，让分页循环结束
                    }
                },
                // 数据处理函数
                data -> {
                    GoodsDO erpGoods = goodsConverter.erpResponseToDo(data);
                    if (StringUtils.isBlank(erpGoods.getErpCode())) {
                        throw BizException.of("物料代码不能为空");
                    }
                    goodsDao.getByErpCode(erpGoods.getErpCode())
                            .ifPresentOrElse(
                                    goods -> {
                                        // 更新现有商品
                                        erpGoods.setId(goods.getId());
                                        goodsDao.updateById(goods.getId(), erpGoods);
                                        log.debug("更新商品: {}", erpGoods.getErpCode());
                                    },
                                    () -> {
                                        // 新增商品
                                        goodsDao.save(erpGoods);
                                        log.debug("新增商品: {}", erpGoods.getErpCode());
                                    }
                            );
                },
                // 异常处理函数
                (data, exception) -> {
                    log.error("处理商品数据失败 - 商品代码: {}, 异常: {}",
                            data.getItemCode(), exception.getMessage(), exception);
                    // 可以在这里记录失败的商品信息，用于后续补偿处理
                }
        );
    }

}
