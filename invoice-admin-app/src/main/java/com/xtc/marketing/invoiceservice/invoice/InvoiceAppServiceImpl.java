package com.xtc.marketing.invoiceservice.invoice;

import com.mybatisflex.core.paginate.Page;
import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.invoiceservice.exception.BizErrorCode;
import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.invoice.converter.InvoiceConverter;
import com.xtc.marketing.invoiceservice.invoice.dao.InvoiceApplyDao;
import com.xtc.marketing.invoiceservice.invoice.dao.InvoiceDao;
import com.xtc.marketing.invoiceservice.invoice.dao.InvoiceItemDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceApplyDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceItemDO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceApplyDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceItemDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceExportCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.*;
import com.xtc.marketing.invoiceservice.invoice.exceldto.InvoiceExportDTO;
import com.xtc.marketing.invoiceservice.support.domainservice.TaskService;
import com.xtc.marketing.invoiceservice.util.CollectionCopier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 发票应用服务实现类
 * <p>简单逻辑 10 行以内的代码可以直接在 service 方法内实现</p>
 * <p>复杂逻辑（或可复用逻辑）封装在 executor 里实现，调用链路：service > executor</p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InvoiceAppServiceImpl implements InvoiceAppService {

    // 基础设施注入
    private final InvoiceConverter invoiceConverter;
    private final InvoiceApplyDao invoiceApplyDao;
    private final InvoiceDao invoiceDao;
    private final InvoiceItemDao invoiceItemDao;
    // 领域服务注入
    private final TaskService taskService;
    // Feign注入
    private final InvoiceFeignClient invoiceFeignClient;

    @Override
    public PageResponse<InvoiceApplyDTO> pageInvoiceApplies(InvoiceApplyPageQry qry) {
        Page<InvoiceApplyDO> page = invoiceApplyDao.pageBy(qry);
        List<InvoiceApplyDTO> records = invoiceConverter.toInvoiceApplyDTO(page.getRecords());
        return PageResponse.of(records, page.getTotalRow(), page.getPageSize(), page.getPageNumber());
    }

    @Override
    public PageResponse<InvoiceDTO> pageInvoices(InvoicePageQry qry) {
        Page<InvoiceDO> page = invoiceDao.pageBy(qry);
        List<InvoiceDTO> records = invoiceConverter.toInvoiceDTO(page.getRecords());
        return PageResponse.of(records, page.getTotalRow(), page.getPageSize(), page.getPageNumber());
    }

    @Override
    public List<InvoiceItemDTO> listInvoiceItems(InvoiceItemListQry qry) {
        // 参数校验
        boolean invoiceNotExists = invoiceDao.notExistsByInvoiceId(qry.getInvoiceId());
        if (invoiceNotExists) {
            throw BizException.of(BizErrorCode.B_INVOICE_InvoiceNotExists);
        }
        // 查询发票项目列表
        List<InvoiceItemDO> invoiceItemDOList = invoiceItemDao.listBy(qry);
        return CollectionCopier.copy(invoiceItemDOList, InvoiceItemDTO::new);
    }

    @Override
    public String invoiceFile(InvoiceFileAdminGetQry qry) {
        InvoiceDetailGetQry invoiceDetailGetQry = InvoiceDetailGetQry.builder().invoiceId(qry.getInvoiceId()).build();
        InvoiceDTO invoiceDTO = invoiceFeignClient.invoiceDetail(invoiceDetailGetQry).getData();
        if (StringUtils.isBlank(invoiceDTO.getFileUrl())) {
            throw BizException.of("未查询到发票文件，请确认文件已同步");
        }
        return invoiceDTO.getFileUrl();
    }

    @Override
    public void exportInvoice(InvoiceExportCmd cmd) {
        long total = invoiceDao.countForExport(cmd);
        taskService.submitExportTask(
                "发票导出",
                cmd,
                total,
                InvoiceExportDTO.class,
                lastData -> {
                    Long lastId = lastData != null ? lastData.getId() : null;
                    List<InvoiceDO> invoice = invoiceDao.listForExport(cmd, lastId);
                    return CollectionCopier.copy(invoice, InvoiceExportDTO::new);
                }
        );
    }

}
