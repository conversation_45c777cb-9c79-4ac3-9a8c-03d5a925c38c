package com.xtc.marketing.invoiceservice.job;

import com.xtc.marketing.invoiceservice.config.BaseJob;
import com.xtc.marketing.invoiceservice.invoice.GoodsAppService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 商品同步任务
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class GoodsSyncJob extends BaseJob {

    private final GoodsAppService goodsAppService;

    /**
     * 商品同步任务
     */
    @XxlJob("goodsSyncJob")
    public void goodsSyncJob() {
        executeShardWithParam(goodsAppService::goodsSyncJob);
    }

}
