package com.xtc.marketing.invoiceservice.api;

import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.dto.Response;
import com.xtc.marketing.dto.SingleResponse;
import com.xtc.marketing.invoiceservice.invoice.SellerAppService;
import com.xtc.marketing.invoiceservice.invoice.dto.SellerDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.SellerCreateCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.SellerEditCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.SellerPageQry;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 销售方接口
 */
@Validated
@RequiredArgsConstructor
@RequestMapping("/api")
@RestController
public class SellerApiController {

    private final SellerAppService sellerAppService;

    /**
     * 销售方分页列表
     *
     * @param qry 参数
     * @return 销售方分页列表
     */
    @GetMapping("/sellers")
    public PageResponse<SellerDTO> pageSellers(@Valid SellerPageQry qry) {
        return sellerAppService.pageSellers(qry);
    }

    /**
     * 销售方详情
     *
     * @param id 销售方id
     * @return 销售方详情
     */
    @GetMapping("/sellers/{id}")
    public SingleResponse<SellerDTO> sellerDetail(@NotNull @Positive @PathVariable("id") Long id) {
        SellerDTO detail = sellerAppService.sellerDetail(id);
        return SingleResponse.of(detail);
    }

    /**
     * 新增销售方
     *
     * @param cmd 参数
     * @return 销售方id
     */
    @PostMapping("/sellers")
    public SingleResponse<Long> createSeller(@Valid @RequestBody SellerCreateCmd cmd) {
        Long id = sellerAppService.createSeller(cmd);
        return SingleResponse.of(id);
    }

    /**
     * 修改销售方
     *
     * @param id  销售方id
     * @param cmd 参数
     * @return 执行结果
     */
    @PutMapping("/sellers/{id}")
    public Response editSeller(@NotNull @Positive @PathVariable("id") Long id,
                               @Valid @RequestBody SellerEditCmd cmd) {
        sellerAppService.editSeller(id, cmd);
        return Response.buildSuccess();
    }

    /**
     * 删除销售方
     *
     * @param id 销售方id
     * @return 执行结果
     */
    @DeleteMapping("/sellers/{id}")
    public Response removeSeller(@NotNull @Positive @PathVariable("id") Long id) {
        sellerAppService.removeSeller(id);
        return Response.buildSuccess();
    }

}
