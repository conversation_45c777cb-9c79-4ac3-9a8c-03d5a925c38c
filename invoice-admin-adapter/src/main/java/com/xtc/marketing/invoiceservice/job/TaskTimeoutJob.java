package com.xtc.marketing.invoiceservice.job;

import com.xtc.marketing.invoiceservice.config.BaseJob;
import com.xtc.marketing.invoiceservice.support.TaskAppService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 任务超时处理任务
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class TaskTimeoutJob extends BaseJob {

    private final TaskAppService taskAppService;

    /**
     * 任务超时处理任务
     */
    @XxlJob("taskTimeoutJob")
    public void taskTimeoutJob() {
        executeShard((shardIndex, shardTotal) -> taskAppService.taskTimeout(shardIndex + 1, shardTotal));
    }

}
