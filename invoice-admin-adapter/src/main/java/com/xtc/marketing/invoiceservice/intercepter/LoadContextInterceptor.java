package com.xtc.marketing.invoiceservice.intercepter;

import com.auth0.jwt.exceptions.TokenExpiredException;
import com.xtc.marketing.invoiceservice.annotation.AllowAnonymous;
import com.xtc.marketing.invoiceservice.context.UserContext;
import com.xtc.marketing.invoiceservice.exception.BizErrorCode;
import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.exception.SysErrorCode;
import com.xtc.marketing.invoiceservice.exception.SysException;
import com.xtc.marketing.invoiceservice.invoice.dao.BusinessDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.BusinessDO;
import com.xtc.marketing.invoiceservice.support.dao.UserDao;
import com.xtc.marketing.invoiceservice.support.dataobject.UserDO;
import com.xtc.marketing.invoiceservice.util.AnnotationCatcher;
import com.xtc.marketing.invoiceservice.util.JwtUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.resource.ResourceHttpRequestHandler;

import javax.security.auth.login.CredentialNotFoundException;
import java.util.Optional;

@Slf4j
@RequiredArgsConstructor
@Component
public class LoadContextInterceptor implements HandlerInterceptor {

    private final UserDao userDao;
    private final BusinessDao businessDao;

    @Value("${xtc.marketing.security.issuer}")
    private String issuer;
    @Value("${xtc.marketing.security.system}")
    private String system;

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) {
        // 过滤资源请求
        if (handler instanceof ResourceHttpRequestHandler) {
            return true;
        }
        // 判断是否需要数据处理
        AllowAnonymous allowAnonymous = AnnotationCatcher.catchHandlerAnnotation(handler, AllowAnonymous.class);
        if (allowAnonymous != null) {
            return true;
        }
        // 鉴权并获取用户工号
        String employeeId = this.verifyTokenAndGetEmployeeId(request);
        // 查询用户
        UserDO userDO = this.getUser(employeeId);
        // 查询用户所属业务
        BusinessDO business = businessDao.getByBizCode(userDO.getBizCode()).orElse(null);
        // 设置用户上下文
        UserContext.User userContext = UserContext.User.builder()
                .employeeId(employeeId)
                .userId(userDO.getUserId())
                .userName(userDO.getUserName())
                .bizCode(userDO.getBizCode())
                .business(business)
                .build();
        UserContext.setUser(userContext);
        return true;
    }

    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
                                @NonNull Object handler, @Nullable Exception ex) {
        // 清除上下文数据，避免 ThreadLocal 内存泄漏
        UserContext.remove();
    }

    /**
     * 获取用户数据
     *
     * @param employeeId 工号
     * @return 用户数据
     */
    private UserDO getUser(String employeeId) {
        UserDO userDO = userDao.getByEmployeeId(employeeId)
                .orElseThrow(() -> BizException.of(BizErrorCode.B_USER_UserNotExists));
        if (BooleanUtils.isNotTrue(userDO.getEnabled())) {
            throw BizException.of(BizErrorCode.B_USER_UserDisabled);
        }
        return userDO;
    }

    /**
     * 鉴权并获取用户工号
     *
     * @param request 请求
     * @return 用户工号
     */
    private String verifyTokenAndGetEmployeeId(HttpServletRequest request) {
        try {
            // 从请求头中获取 jwt
            Optional<String> jwt = JwtUtil.getJwt(request);
            if (jwt.isEmpty()) {
                throw new CredentialNotFoundException("请求头缺少鉴权数据");
            }
            // 验证 jwt 并获取用户工号
            Optional<String> employeeId = JwtUtil.verifyAndGetUsername(jwt.get(), issuer, system);
            if (employeeId.isEmpty()) {
                throw new CredentialNotFoundException("鉴权数据缺少工号");
            }
            return employeeId.get();
        } catch (TokenExpiredException e) {
            throw SysException.of(SysErrorCode.S_LOGIN_LoginError.getErrCode(), "登录已过期");
        } catch (Exception e) {
            throw SysException.of(SysErrorCode.S_LOGIN_LoginError, e.getMessage(), e);
        }
    }

}
