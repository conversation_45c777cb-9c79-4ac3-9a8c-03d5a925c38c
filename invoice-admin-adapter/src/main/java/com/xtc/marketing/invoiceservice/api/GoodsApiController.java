package com.xtc.marketing.invoiceservice.api;

import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.dto.Response;
import com.xtc.marketing.dto.SingleResponse;
import com.xtc.marketing.invoiceservice.invoice.GoodsAppService;
import com.xtc.marketing.invoiceservice.invoice.dto.GoodsDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.GoodsCreateCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.GoodsEditCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.GoodsPageQry;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 商品接口
 */
@Validated
@RequiredArgsConstructor
@RequestMapping("/api")
@RestController
public class GoodsApiController {

    private final GoodsAppService goodsAppService;

    /**
     * 商品分页列表
     *
     * @param qry 参数
     * @return 商品分页列表
     */
    @GetMapping("/goods")
    public PageResponse<GoodsDTO> pageGoods(@Valid GoodsPageQry qry) {
        return goodsAppService.pageGoods(qry);
    }

    /**
     * 商品详情
     *
     * @param id 商品id
     * @return 商品详情
     */
    @GetMapping("/goods/{id}")
    public SingleResponse<GoodsDTO> goodsDetail(@NotNull @Positive @PathVariable("id") Long id) {
        GoodsDTO detail = goodsAppService.goodsDetail(id);
        return SingleResponse.of(detail);
    }

    /**
     * 新增商品
     *
     * @param cmd 参数
     * @return 商品id
     */
    @PostMapping("/goods")
    public SingleResponse<Long> createGoods(@Valid @RequestBody GoodsCreateCmd cmd) {
        Long id = goodsAppService.createGoods(cmd);
        return SingleResponse.of(id);
    }

    /**
     * 修改商品
     *
     * @param id  商品id
     * @param cmd 参数
     * @return 执行结果
     */
    @PutMapping("/goods/{id}")
    public Response editGoods(@NotNull @Positive @PathVariable("id") Long id,
                              @Valid @RequestBody GoodsEditCmd cmd) {
        goodsAppService.editGoods(id, cmd);
        return Response.buildSuccess();
    }

    /**
     * 删除商品
     *
     * @param id 商品id
     * @return 执行结果
     */
    @DeleteMapping("/goods/{id}")
    public Response removeGoods(@NotNull @Positive @PathVariable("id") Long id) {
        goodsAppService.removeGoods(id);
        return Response.buildSuccess();
    }

    /**
     * 商品导出任务
     *
     * @return 执行结果
     */
    @PostMapping("/goods/export")
    public Response exportGoods() {
        goodsAppService.exportGoods();
        return Response.buildSuccess();
    }

    /**
     * 商品导入任务
     *
     * @param file 文件
     * @return 执行结果
     */
    @PostMapping("/goods/import")
    public Response importGoods(@NotNull @RequestParam("file") MultipartFile file) {
        goodsAppService.importGoods(file);
        return Response.buildSuccess();
    }

}
