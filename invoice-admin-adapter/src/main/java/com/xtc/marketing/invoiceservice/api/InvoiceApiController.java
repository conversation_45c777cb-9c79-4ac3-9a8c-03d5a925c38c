package com.xtc.marketing.invoiceservice.api;

import com.xtc.marketing.dto.MultiResponse;
import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.dto.Response;
import com.xtc.marketing.dto.SingleResponse;
import com.xtc.marketing.invoiceservice.invoice.InvoiceAppService;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceApplyDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceItemDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceExportCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceApplyPageQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceFileAdminGetQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceItemListQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoicePageQry;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 发票接口
 */
@Validated
@RequiredArgsConstructor
@RequestMapping("/api")
@RestController
public class InvoiceApiController {

    private final InvoiceAppService invoiceAppService;

    /**
     * 发票申请分页列表
     *
     * @param qry 参数
     * @return 发票申请分页列表
     */
    @GetMapping("/invoice-applies")
    public PageResponse<InvoiceApplyDTO> pageInvoiceApplies(@Valid InvoiceApplyPageQry qry) {
        return invoiceAppService.pageInvoiceApplies(qry);
    }

    /**
     * 发票分页列表
     *
     * @param qry 参数
     * @return 发票分页列表
     */
    @GetMapping("/invoices")
    public PageResponse<InvoiceDTO> pageInvoices(@Valid InvoicePageQry qry) {
        return invoiceAppService.pageInvoices(qry);
    }

    /**
     * 发票项目列表
     *
     * @param qry 参数
     * @return 发票项目列表
     */
    @GetMapping("/invoice-items")
    public MultiResponse<InvoiceItemDTO> listInvoiceItems(@Valid InvoiceItemListQry qry) {
        List<InvoiceItemDTO> dto = invoiceAppService.listInvoiceItems(qry);
        return MultiResponse.of(dto);
    }

    /**
     * 发票文件地址
     *
     * @param qry 参数
     * @return 发票文件地址
     */
    @GetMapping("/invoice/file")
    public SingleResponse<String> invoiceFile(@Valid InvoiceFileAdminGetQry qry) {
        String fileUrl = invoiceAppService.invoiceFile(qry);
        return SingleResponse.of(fileUrl);
    }

    /**
     * 发票导出
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/invoice/export")
    public Response exportInvoice(@Valid @RequestBody InvoiceExportCmd cmd) {
        invoiceAppService.exportInvoice(cmd);
        return Response.buildSuccess();
    }

}
