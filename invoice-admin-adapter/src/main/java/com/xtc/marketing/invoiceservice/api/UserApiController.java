package com.xtc.marketing.invoiceservice.api;

import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.dto.Response;
import com.xtc.marketing.dto.SingleResponse;
import com.xtc.marketing.invoiceservice.support.UserAppService;
import com.xtc.marketing.invoiceservice.support.dto.UserDTO;
import com.xtc.marketing.invoiceservice.support.dto.command.UserCreateCmd;
import com.xtc.marketing.invoiceservice.support.dto.command.UserEditCmd;
import com.xtc.marketing.invoiceservice.support.dto.query.UserPageQry;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户接口
 */
@Validated
@RequiredArgsConstructor
@RequestMapping("/api")
@RestController
public class UserApiController {

    private final UserAppService userAppService;

    /**
     * 用户分页列表
     *
     * @param qry 参数
     * @return 用户分页列表
     */
    @GetMapping("/users")
    public PageResponse<UserDTO> pageUsers(@Valid UserPageQry qry) {
        return userAppService.pageUsers(qry);
    }

    /**
     * 用户详情
     *
     * @param id 用户id
     * @return 用户详情
     */
    @GetMapping("/users/{id}")
    public SingleResponse<UserDTO> userDetail(@NotNull @Positive @PathVariable("id") Long id) {
        UserDTO detail = userAppService.userDetail(id);
        return SingleResponse.of(detail);
    }

    /**
     * 新增用户
     *
     * @param cmd 参数
     * @return 用户id
     */
    @PostMapping("/users")
    public SingleResponse<Long> createUser(@Valid @RequestBody UserCreateCmd cmd) {
        Long id = userAppService.createUser(cmd);
        return SingleResponse.of(id);
    }

    /**
     * 修改用户
     *
     * @param id  用户id
     * @param cmd 参数
     * @return 执行结果
     */
    @PutMapping("/users/{id}")
    public Response editUser(@NotNull @Positive @PathVariable("id") Long id,
                             @Valid @RequestBody UserEditCmd cmd) {
        userAppService.editUser(id, cmd);
        return Response.buildSuccess();
    }

    /**
     * 删除用户
     *
     * @param id 用户id
     * @return 执行结果
     */
    @DeleteMapping("/users/{id}")
    public Response removeUser(@NotNull @Positive @PathVariable("id") Long id) {
        userAppService.removeUser(id);
        return Response.buildSuccess();
    }

}
