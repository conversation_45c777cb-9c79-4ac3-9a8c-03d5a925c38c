package com.xtc.marketing.invoiceservice.api;

import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.dto.Response;
import com.xtc.marketing.dto.SingleResponse;
import com.xtc.marketing.invoiceservice.invoice.BusinessAppService;
import com.xtc.marketing.invoiceservice.invoice.dto.BusinessDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.BusinessCreateCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.BusinessEditCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.BusinessPageQry;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 业务接入接口
 */
@Validated
@RequiredArgsConstructor
@RequestMapping("/api")
@RestController
public class BusinessApiController {

    private final BusinessAppService businessAppService;

    /**
     * 业务接入分页列表
     *
     * @param qry 参数
     * @return 业务接入分页列表
     */
    @GetMapping("/businesses")
    public PageResponse<BusinessDTO> pageBusinesses(@Valid BusinessPageQry qry) {
        return businessAppService.pageBusinesses(qry);
    }

    /**
     * 业务接入详情
     *
     * @param id 业务接入id
     * @return 业务接入详情
     */
    @GetMapping("/businesses/{id}")
    public SingleResponse<BusinessDTO> businessDetail(@NotNull @Positive @PathVariable("id") Long id) {
        BusinessDTO detail = businessAppService.businessDetail(id);
        return SingleResponse.of(detail);
    }

    /**
     * 新增业务接入
     *
     * @param cmd 参数
     * @return 业务接入id
     */
    @PostMapping("/businesses")
    public SingleResponse<Long> createBusiness(@Valid @RequestBody BusinessCreateCmd cmd) {
        Long id = businessAppService.createBusiness(cmd);
        return SingleResponse.of(id);
    }

    /**
     * 编辑业务接入
     *
     * @param id  业务接入id
     * @param cmd 参数
     * @return 执行结果
     */
    @PutMapping("/businesses/{id}")
    public Response editBusiness(@NotNull @Positive @PathVariable("id") Long id,
                                 @Valid @RequestBody BusinessEditCmd cmd) {
        businessAppService.editBusiness(id, cmd);
        return Response.buildSuccess();
    }

    /**
     * 删除业务接入
     *
     * @param id 业务接入id
     * @return 执行结果
     */
    @DeleteMapping("/businesses/{id}")
    public Response removeBusiness(@NotNull @Positive @PathVariable("id") Long id) {
        businessAppService.removeBusiness(id);
        return Response.buildSuccess();
    }

}
